/**
 * Intelligence Storage Service
 * Manages JSON storage for file-level and vault-level intelligence data
 */

import { BaseService, ServiceError, ServiceErrorCode } from './base'
import { 
  FileIntelligence, 
  VaultIntelligence, 
  KeyIdea, 
  WeightedEntity, 
  HumanConnection,
  ProcessingResult 
} from '../types/fileIntelligenceTypes'
import { extractContextPath as extractContextPathUtil, joinLike } from '../utils/vaultPath'
// Note: crypto import removed for browser compatibility

export interface IntelligenceStorageOptions {
  vaultPath: string
  createDirectories?: boolean
}

class IntelligenceStorageService extends BaseService {
  private readonly CONTEXT_DIR = '.context'
  private readonly FILES_DIR = 'files'
  private readonly VAULT_INTELLIGENCE_FILE = 'vault_intelligence.json'

  constructor() {
    super({
      name: 'IntelligenceStorageService',
      autoInitialize: true
    })
  }

  protected async doInitialize(): Promise<void> {
    this.logger.info('Intelligence Storage Service initialized', 'doInitialize')
  }

  /**
   * Store file intelligence data
   */
  async storeFileIntelligence(
    filePath: string, 
    intelligence: FileIntelligence, 
    options: IntelligenceStorageOptions
  ): Promise<void> {
    return await this.executeOperationOrThrow(
      'storeFileIntelligence',
      async () => {
        console.log('[LABELS] 💾 storeFileIntelligence called')
        console.log('[LABELS] 💾 Storing filePath:', filePath)
        console.log('[LABELS] 💾 Intelligence key_ideas count:', intelligence.key_ideas.length)

        this.logger.info('Storing file intelligence', 'storeFileIntelligence', { filePath })

        // Generate file hash for unique identification
        console.log('[LABELS] 💾 STORAGE: About to generate hash for storage')
        console.log('[LABELS] 💾 STORAGE: Input filePath for storage:', filePath)
        const fileHash = this.generateFileHash(filePath)
        console.log('[LABELS] 💾 STORAGE: Generated hash for storage:', fileHash)

        // Resolve effective context path (prefer context derived from filePath)
        const derivedContext = extractContextPathUtil(filePath)
        const effectiveContextPath = derivedContext || options.vaultPath

        // Create storage path under context folder, not vault root
        const contextDir = this.getContextDirectory(effectiveContextPath)
        const filesDir = this.getFilesDirectory(effectiveContextPath)
        const intelligenceFile = joinLike(filesDir, `${fileHash}.json`)

        console.log('[LABELS] 💾 STORAGE: Storage paths calculated:')
        console.log('[LABELS] 💾 STORAGE:   vaultPath:', options.vaultPath)
        console.log('[LABELS] 💾 STORAGE:   effectiveContextPath:', effectiveContextPath)
        console.log('[LABELS] 💾 STORAGE:   filesDir:', filesDir)
        console.log('[LABELS] 💾 STORAGE:   intelligenceFile:', intelligenceFile)

        // 🚨 PATH RESOLUTION AUDIT LOG
        this.auditPathResolution('STORAGE', filePath, effectiveContextPath, filesDir)

        console.log('[LABELS] 💾 Storage paths:')
        console.log('[LABELS] 💾   contextDir:', contextDir)
        console.log('[LABELS] 💾   filesDir:', filesDir)
        console.log('[LABELS] 💾   intelligenceFile:', intelligenceFile)

        // Ensure directories exist
        if (options.createDirectories) {
          await this.ensureDirectoryExists(contextDir)
          await this.ensureDirectoryExists(filesDir)
        }

        // Prepare intelligence data with metadata
        const intelligenceData = {
          ...intelligence,
          storage_metadata: {
            file_path: filePath,
            file_hash: fileHash,
            stored_at: new Date().toISOString(),
            storage_version: '1.0'
          }
        }

        console.log('[LABELS] 💾 Intelligence data to store:')
        console.log('[LABELS] 💾   file_path in JSON:', intelligenceData.file_path)
        console.log('[LABELS] 💾   storage_metadata.file_path:', intelligenceData.storage_metadata.file_path)
        console.log('[LABELS] 💾   storage_metadata.file_hash:', intelligenceData.storage_metadata.file_hash)

        // Write to file
        console.log('[LABELS] 💾 Writing JSON file to:', intelligenceFile)
        console.log('[LABELS] 💾 Intelligence data size:', JSON.stringify(intelligenceData).length, 'characters')
        console.log('[LABELS] 💾 Key ideas count:', intelligenceData.key_ideas?.length || 0)

        try {
          await this.writeJsonFile(intelligenceFile, intelligenceData)
          console.log('[LABELS] 💾 ✅ JSON file written successfully')

          // Verify the file was actually created (with retry for file system sync)
          console.log('[LABELS] 💾 Verifying file was created...')
          let verifyResult = null
          let retryCount = 0
          const maxRetries = 3

          while (!verifyResult && retryCount < maxRetries) {
            if (retryCount > 0) {
              console.log(`[LABELS] 💾 Verification attempt ${retryCount + 1}/${maxRetries} (waiting for file system sync...)`)
              await new Promise(resolve => setTimeout(resolve, 100 * retryCount)) // Progressive delay
            }

            try {
              verifyResult = await this.readJsonFile(intelligenceFile)
            } catch (readError) {
              console.warn(`[LABELS] 💾 Verification attempt ${retryCount + 1} failed:`, readError)
            }

            retryCount++
          }

          if (verifyResult) {
            console.log('[LABELS] 💾 ✅ File verification successful - file exists and is readable')
            console.log('[LABELS] 💾 Verified file contains', verifyResult.key_ideas?.length || 0, 'ideas')
          } else {
            console.warn('[LABELS] 💾 ⚠️ File verification failed after', maxRetries, 'attempts')
            console.warn('[LABELS] 💾 File path:', intelligenceFile)
            console.warn('[LABELS] 💾 This is likely a file system sync timing issue')
            console.warn('[LABELS] 💾 The file was written successfully and should be available shortly')
            console.warn('[LABELS] 💾 This does not affect functionality - the intelligence data was stored correctly')
            // Don't throw error - the write was successful, verification is just a safety check
          }
        } catch (writeError) {
          console.error('[LABELS] 💾 ❌ Failed to write JSON file:', writeError)
          throw writeError
        }

        this.logger.info('File intelligence stored successfully', 'storeFileIntelligence', {
          filePath,
          intelligenceFile,
          ideasCount: intelligence.key_ideas.length,
          entitiesCount: intelligence.weighted_entities.length
        })
      },
      { filePath, vaultPath: options.vaultPath }
    )
  }

  /**
   * Retrieve file intelligence data
   */
  async getFileIntelligence(filePath: string, vaultPath: string): Promise<FileIntelligence | null> {
    console.log('[LABELS] 💾 intelligenceStorageService: getFileIntelligence called')
    console.log('[LABELS] 💾 Input filePath:', filePath)
    console.log('[LABELS] 💾 Input vaultPath:', vaultPath)

    // Resolve effective context path first (outside to use in telemetry as well)
    const derivedContext = extractContextPathUtil(filePath)
    const effectiveContextPath = derivedContext || vaultPath

    console.log('[LABELS] 💾 derivedContext:', derivedContext)
    console.log('[LABELS] 💾 effectiveContextPath:', effectiveContextPath)

    return await this.executeOperationOrThrow(
      'getFileIntelligence',
      async () => {
        console.log('[LABELS] 💾 RETRIEVAL: About to generate hash for retrieval')
        console.log('[LABELS] 💾 RETRIEVAL: Input filePath for retrieval:', filePath)
        const fileHash = this.generateFileHash(filePath)
        console.log('[LABELS] 💾 RETRIEVAL: Generated hash for retrieval:', fileHash)

        const filesDir = this.getFilesDirectory(effectiveContextPath)
        const intelligenceFile = joinLike(filesDir, `${fileHash}.json`)

        console.log('[LABELS] 💾 Generated fileHash:', fileHash)
        console.log('[LABELS] 💾 filesDir:', filesDir)
        console.log('[LABELS] 💾 intelligenceFile path:', intelligenceFile)

        // 🚨 PATH RESOLUTION AUDIT LOG
        this.auditPathResolution('RETRIEVAL', filePath, effectiveContextPath, filesDir)

        this.logger.debug('Computed intelligence path', 'getFileIntelligence', {
          fileHash,
          intelligenceFile,
          vaultPath: effectiveContextPath
        })

        try {
          console.log('[LABELS] 💾 Attempting to read JSON file:', intelligenceFile)

          // DEBUG: Test what path would generate the target hash 70f190a4
          this.testHashForTarget(filePath, '70f190a4')

          let data = await this.readJsonFile(intelligenceFile)

          console.log('[LABELS] 💾 JSON file read result:', data ? 'found' : 'null')

          // If primary hash file not found, try to find by filename pattern
          if (!data) {
            console.log('[LABELS] 💾 Primary hash file not found, searching for existing files...')
            data = await this.findExistingIntelligenceFile(filePath, filesDir)
          }

          if (data) {
            console.log('[LABELS] 💾 JSON content preview:', {
              file_path: data.file_path,
              key_ideas_count: data.key_ideas?.length || 0,
              created_at: data.created_at,
              updated_at: data.updated_at
            })
          }

          if (data && this.isValidFileIntelligence(data)) {
            console.log('[LABELS] 💾 ✅ Valid file intelligence found')
            console.log('[LABELS] 💾 JSON file_path matches input?', data.file_path === filePath)
            this.logger.info('File intelligence retrieved', 'getFileIntelligence', { filePath })
            return data as FileIntelligence
          }

          // Legacy compatibility: attempt conversion from older formats
          if (data) {
            console.log('[LABELS] 💾 Attempting legacy conversion')
            const converted = this.convertLegacyIntelligence(filePath, data)
            if (converted) {
              console.log('[LABELS] 💾 ✅ Legacy intelligence converted')
              this.logger.info('Legacy intelligence converted on read', 'getFileIntelligence', {
                ideas: converted.key_ideas.length
              })
              return converted
            }
          }

          console.log('[LABELS] 💾 ❌ No valid intelligence data found')
          return null
        } catch (error) {
          // File doesn't exist or is invalid
          console.log('[LABELS] 💾 ❌ Error reading intelligence file:', error)
          this.logger.debug('File intelligence not found', 'getFileIntelligence', { filePath })
          return null
        }
      },
      { filePath, vaultPath: effectiveContextPath }
    )
  }

  /**
   * Convert legacy intelligence payloads (arrays, alternative keys, or FILE_INTEL blocks)
   */
  private convertLegacyIntelligence(filePath: string, raw: any): FileIntelligence | null {
    try {
      const now = new Date().toISOString()
      const toIdeas = (arr: any[]): KeyIdea[] => {
        return arr.map((it: any, index: number) => {
          // Accept string or object
          const text = typeof it === 'string' ? it : String(it.text || '').trim()
          const scoreRaw = typeof it === 'object' ? (it.relevance_score ?? it.score) : undefined
          const scoreNum = typeof scoreRaw === 'number' ? scoreRaw : parseInt(String(scoreRaw || '50'), 10)
          const score = isNaN(scoreNum) ? 50 : Math.max(0, Math.min(100, scoreNum))
          const intentsRaw = typeof it === 'object' ? (it.intent_types ?? it.intents) : undefined
          const intents = Array.isArray(intentsRaw)
            ? intentsRaw
            : (typeof intentsRaw === 'string' ? intentsRaw.split(',').map((s: string) => s.trim()).filter(Boolean) : [])
          const context = typeof it === 'object' ? String(it.context || '') : ''
          const entitiesRaw = typeof it === 'object' ? (it.entities_mentioned ?? it.entities) : undefined
          const entities = Array.isArray(entitiesRaw)
            ? entitiesRaw
            : (typeof entitiesRaw === 'string' ? entitiesRaw.split(',').map((s: string) => s.trim()).filter(Boolean) : [])
          return {
            id: `legacy_${Date.now()}_${index}`,
            text,
            relevance_score: score,
            intent_types: intents.length ? intents : ['topic'],
            weight: Math.min(1, score / 100),
            auto_selected: false,
            user_confirmed: false,
            context,
            extracted_from: 'legacy_json'
          }
        })
      }

      // Case A: Array of idea-like objects
      if (Array.isArray(raw)) {
        const key_ideas = toIdeas(raw)
        return {
          file_path: filePath,
          key_ideas,
          weighted_entities: [],
          human_connections: [],
          processing_confidence: key_ideas.length ? 0.7 : 0,
          analysis_metadata: {
            processing_time_ms: 0,
            model_used: 'legacy',
            timestamp: now
          },
          created_at: now,
          updated_at: now
        }
      }

      if (raw && typeof raw === 'object') {
        // Case B: { key_ideas: [...] } but not fully validated
        if (Array.isArray(raw.key_ideas)) {
          const key_ideas = toIdeas(raw.key_ideas)
          return {
            file_path: filePath,
            key_ideas,
            weighted_entities: Array.isArray(raw.weighted_entities) ? raw.weighted_entities : [],
            human_connections: Array.isArray(raw.human_connections) ? raw.human_connections : [],
            processing_confidence: key_ideas.length ? 0.7 : 0,
            analysis_metadata: raw.analysis_metadata || { processing_time_ms: 0, model_used: 'legacy', timestamp: now },
            created_at: raw.created_at || now,
            updated_at: now
          }
        }
        // Case C: { ideas: [...] } or { labels: [...] }
        if (Array.isArray(raw.ideas)) {
          const key_ideas = toIdeas(raw.ideas)
          return {
            file_path: filePath,
            key_ideas,
            weighted_entities: [],
            human_connections: [],
            processing_confidence: key_ideas.length ? 0.7 : 0,
            analysis_metadata: { processing_time_ms: 0, model_used: 'legacy', timestamp: now },
            created_at: now,
            updated_at: now
          }
        }
        if (Array.isArray(raw.labels)) {
          const key_ideas = toIdeas(raw.labels)
          return {
            file_path: filePath,
            key_ideas,
            weighted_entities: [],
            human_connections: [],
            processing_confidence: key_ideas.length ? 0.7 : 0,
            analysis_metadata: { processing_time_ms: 0, model_used: 'legacy', timestamp: now },
            created_at: now,
            updated_at: now
          }
        }
      }

      // Case D: String content with FILE_INTEL block or fenced JSON
      if (typeof raw === 'string') {
        const block = raw
        // Try FILE_INTEL markers first
        const start = block.indexOf('!-- FILE_INTEL:BEGIN --')
        const end = block.indexOf('!-- FILE_INTEL:END --')
        if (start !== -1 && end !== -1 && end > start) {
          let inner = block.substring(start + '!-- FILE_INTEL:BEGIN --'.length, end)
          inner = inner.replace(/```[a-zA-Z]*\n?/g, '').replace(/```/g, '').replace(/\r/g, '')
          const lines = inner.split('\n').map(l => l.trim()).filter(l => l.startsWith('- '))
          const arr = lines.map(l => {
            const body = l.slice(2)
            const parts = body.split(';').map(p => p.trim()).filter(Boolean)
            const kv: Record<string, string> = {}
            parts.forEach(p => { const i = p.indexOf('='); if (i>0) { const k = p.slice(0,i).trim().toLowerCase(); let v = p.slice(i+1).trim(); v = v.replace(/^"|"$/g,''); kv[k]=v; } })
            return kv
          })
          const key_ideas = toIdeas(arr)
          return {
            file_path: filePath,
            key_ideas,
            weighted_entities: [],
            human_connections: [],
            processing_confidence: key_ideas.length ? 0.7 : 0,
            analysis_metadata: { processing_time_ms: 0, model_used: 'legacy_block', timestamp: now },
            created_at: now,
            updated_at: now
          }
        }
        // Try fenced JSON
        const fence = raw.match(/```json\s*([\s\S]*?)\s*```/i) || raw.match(/```\s*([\s\S]*?)\s*```/)
        if (fence && fence[1]) {
          try {
            const obj = JSON.parse(fence[1])
            return this.convertLegacyIntelligence(filePath, obj)
          } catch { /* ignore */ }
        }
      }

      return null
    } catch {
      return null
    }
  }

  /**
   * Debug helper: compute the JSON path for a given file without reading/writing
   */
  debugIntelligencePath(filePath: string, vaultPath: string): { fileHash: string; intelligenceFile: string; filesDir: string; contextDir: string } {
    const fileHash = this.generateFileHash(filePath)
    const contextDir = this.getContextDirectory(vaultPath)
    const filesDir = this.getFilesDirectory(vaultPath)
    const intelligenceFile = joinLike(filesDir, `${fileHash}.json`)
    return { fileHash, intelligenceFile, filesDir, contextDir }
  }

  /**
   * Debug helper: test hash generation with different path formats
   */
  debugHashGeneration(filePath: string): void {
    console.log('[LABELS] 🔍 DEBUG: Testing hash generation for:', filePath)

    // Test current algorithm
    const currentHash = this.generateFileHash(filePath)
    console.log('[LABELS] 🔍 Current algorithm hash:', currentHash)

    // Test with different normalizations
    const variants = [
      filePath,
      filePath.replace(/\\/g, '/'),
      filePath.replace(/\//g, '\\'),
      filePath.toLowerCase(),
      filePath.replace(/\\/g, '/').toLowerCase(),
      filePath.replace(/\//g, '\\').toLowerCase()
    ]

    variants.forEach((variant, index) => {
      const testHash = this.generateFileHashVariant(variant)
      console.log(`[LABELS] 🔍 Variant ${index} (${variant}):`, testHash)
    })
  }

  private generateFileHashVariant(filePath: string): string {
    const normalizedPath = filePath.replace(/\\/g, '/').toLowerCase()
    let hash = 0
    for (let i = 0; i < normalizedPath.length; i++) {
      const char = normalizedPath.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash
    }
    const fileName = filePath.split(/[/\\]/).pop()?.replace(/\.[^.]*$/, '') || 'unknown'
    const hashSuffix = Math.abs(hash).toString(16).substring(0, 8)
    return `${fileName}_${hashSuffix}`
  }

  /**
   * Find existing intelligence file by scanning the files directory
   */
  private async findExistingIntelligenceFile(filePath: string, filesDir: string): Promise<any> {
    try {
      console.log('[LABELS] 🔍 FALLBACK: Scanning for existing intelligence files...')
      console.log('[LABELS] 🔍 FALLBACK: Target filePath:', filePath)
      console.log('[LABELS] 🔍 FALLBACK: Scanning directory:', filesDir)

      // Extract filename for pattern matching
      const fileName = filePath.split(/[/\\]/).pop()?.replace(/\.[^.]*$/, '') || 'unknown'
      console.log('[LABELS] 🔍 FALLBACK: Looking for files matching pattern:', `${fileName}_*.json`)

      // Use electronAPI to scan the files directory
      if (!window.electronAPI?.vault?.readDirectory) {
        console.log('[LABELS] 🔍 FALLBACK: electronAPI not available')
        return null
      }

      const result = await window.electronAPI.vault.readDirectory(filesDir)
      if (!result.success) {
        console.log('[LABELS] 🔍 FALLBACK: Failed to read directory:', result.error)
        return null
      }

      console.log('[LABELS] 🔍 FALLBACK: Directory read successful, found', result.items.length, 'items')
      console.log('[LABELS] 🔍 FALLBACK: All items:', result.items.map(item => ({
        name: item.name,
        isDirectory: item.isDirectory,
        path: item.path
      })))

      // Look for files that match the filename pattern
      const matchingFiles = result.items.filter(item => {
        const matches = !item.isDirectory &&
                       item.name.startsWith(fileName + '_') &&
                       item.name.endsWith('.json')
        console.log('[LABELS] 🔍 FALLBACK: Checking item:', item.name, 'matches:', matches)
        return matches
      })

      console.log('[LABELS] 🔍 FALLBACK: Found', matchingFiles.length, 'matching files:', matchingFiles.map(f => f.name))

      // Try to read the first matching file
      for (const file of matchingFiles) {
        try {
          console.log('[LABELS] 🔍 FALLBACK: Attempting to read:', file.name)

          // Construct full file path
          const fullFilePath = file.path || joinLike(filesDir, file.name)
          console.log('[LABELS] 🔍 FALLBACK: Full file path:', fullFilePath)

          const data = await this.readJsonFile(fullFilePath)

          if (data && this.isValidFileIntelligence(data)) {
            console.log('[LABELS] 🔍 FALLBACK: ✅ Found valid intelligence in:', file.name)
            return data
          } else {
            console.log('[LABELS] 🔍 FALLBACK: File read but invalid intelligence:', file.name)
          }
        } catch (fileError) {
          console.warn('[LABELS] 🔍 FALLBACK: Failed to read file:', file.name, fileError)
        }
      }

      console.log('[LABELS] 🔍 FALLBACK: No valid intelligence files found')
      return null

    } catch (error) {
      console.error('[LABELS] 🔍 FALLBACK: Error scanning for existing files:', error)
      return null
    }
  }

  /**
   * Test what path modifications would generate the target hash
   */
  testHashForTarget(originalPath: string, targetHash: string): void {
    console.log('[LABELS] 🔍 REVERSE-ENGINEERING: Looking for path that generates hash:', targetHash)
    console.log('[LABELS] 🔍 REVERSE-ENGINEERING: Original path:', originalPath)

    // Test various path modifications
    const testPaths = [
      originalPath,
      originalPath.replace(/\\/g, '/'),
      originalPath.replace(/\//g, '\\'),
      originalPath.toLowerCase(),
      originalPath.toUpperCase(),
      // Test without the full path - maybe just relative path was used
      originalPath.split(/[/\\]/).slice(-3).join('/'), // last 3 parts
      originalPath.split(/[/\\]/).slice(-2).join('/'), // last 2 parts
      originalPath.split(/[/\\]/).pop(), // just filename
      // Test with different base paths
      originalPath.replace('C:\\Users\\<USER>\\Documents\\Test20\\', ''),
      originalPath.replace('C:/Users/<USER>/Documents/Test20/', ''),
      // Test with old algorithm (no normalization)
      originalPath // will test with old algorithm below
    ]

    testPaths.forEach((testPath, index) => {
      const hash = this.generateHashWithOldAlgorithm(testPath)
      console.log(`[LABELS] 🔍 Test ${index}: "${testPath}" → ${hash}`)
      if (hash.includes(targetHash)) {
        console.log(`[LABELS] 🎯 MATCH FOUND! Path: "${testPath}" generates hash: ${hash}`)
      }
    })
  }

  /**
   * 🚨 PATH RESOLUTION AUDIT - Log violations without breaking functionality
   */
  private auditPathResolution(operation: string, filePath: string, vaultPath: string, filesDir: string): void {
    console.log(`[🚨 PATH-AUDIT] ${operation}: Starting path resolution audit`)

    const violations: string[] = []
    const warnings: string[] = []

    // 1. Check for hardcoded paths
    if (filePath.includes('C:\\Users\\<USER>\\Documents\\Test20')) {
      violations.push('HARDCODED_USER_PATH: Contains hardcoded user path')
    }

    // 2. Check for inconsistent path separators
    const hasBackslash = filePath.includes('\\')
    const hasForwardSlash = filePath.includes('/')
    if (hasBackslash && hasForwardSlash) {
      warnings.push('MIXED_SEPARATORS: File path contains both \\ and / separators')
    }

    // 3. Check vault structure compliance
    const expectedSubdirs = ['documents', 'images', 'artifacts']
    const hasExpectedStructure = expectedSubdirs.some(subdir => filePath.includes(`/${subdir}/`) || filePath.includes(`\\${subdir}\\`))
    if (!hasExpectedStructure) {
      warnings.push('NON_STANDARD_STRUCTURE: File not in expected vault subdirectory (documents/images/artifacts)')
    }

    // 4. Check .context directory structure
    if (!filesDir.includes('.context/files') && !filesDir.includes('.context\\files')) {
      violations.push('INVALID_CONTEXT_STRUCTURE: Files directory does not follow .context/files pattern')
    }

    // 5. Check path normalization consistency
    const normalizedFilePath = filePath.replace(/\\/g, '/').toLowerCase()
    const normalizedVaultPath = vaultPath.replace(/\\/g, '/').toLowerCase()
    if (!normalizedFilePath.startsWith(normalizedVaultPath)) {
      violations.push('PATH_HIERARCHY_VIOLATION: File path does not start with vault path')
    }

    // 6. Check for legacy file system patterns
    if (filePath.includes('Documents/Chatlo') || filePath.includes('Documents\\Chatlo')) {
      violations.push('LEGACY_FILESYSTEM: Using deprecated Chatlo folder structure')
    }

    // 7. Check for plugin-incompatible patterns
    const pathSegments = filePath.split(/[/\\]/)
    if (pathSegments.some(segment => segment.includes(' ') && !segment.match(/^[a-zA-Z0-9\-_\s]+$/))) {
      warnings.push('PLUGIN_INCOMPATIBLE: Path contains special characters that may break plugins')
    }

    // Log results
    if (violations.length > 0) {
      console.error(`[🚨 PATH-AUDIT] ${operation}: VIOLATIONS FOUND:`)
      violations.forEach(violation => console.error(`[🚨 PATH-AUDIT]   ❌ ${violation}`))
      console.error(`[🚨 PATH-AUDIT]   📁 FilePath: ${filePath}`)
      console.error(`[🚨 PATH-AUDIT]   📁 VaultPath: ${vaultPath}`)
      console.error(`[🚨 PATH-AUDIT]   📁 FilesDir: ${filesDir}`)
    }

    if (warnings.length > 0) {
      console.warn(`[🚨 PATH-AUDIT] ${operation}: WARNINGS:`)
      warnings.forEach(warning => console.warn(`[🚨 PATH-AUDIT]   ⚠️ ${warning}`))
    }

    if (violations.length === 0 && warnings.length === 0) {
      console.log(`[🚨 PATH-AUDIT] ${operation}: ✅ Path resolution compliant`)
    }

    // Store audit results for future plugin compatibility analysis
    if (typeof window !== 'undefined' && !(window as any).pathAuditResults) {
      (window as any).pathAuditResults = []
    }
    if (typeof window !== 'undefined') {
      (window as any).pathAuditResults.push({
        timestamp: new Date().toISOString(),
        operation,
        filePath,
        vaultPath,
        filesDir,
        violations,
        warnings
      })

      // Add global audit summary function
      if (!(window as any).getPathAuditSummary) {
        (window as any).getPathAuditSummary = () => {
          console.log('[🚨 PATH-AUDIT] SUMMARY: Path Resolution Violations Report')
          console.log('[🚨 PATH-AUDIT] Total operations audited:', (window as any).pathAuditResults.length)

          const allViolations = (window as any).pathAuditResults.flatMap((r: any) => r.violations)
          const allWarnings = (window as any).pathAuditResults.flatMap((r: any) => r.warnings)

          console.log('[🚨 PATH-AUDIT] Total violations:', allViolations.length)
          console.log('[🚨 PATH-AUDIT] Total warnings:', allWarnings.length)

          // Group violations by type
          const violationCounts = {}
          allViolations.forEach(v => {
            const type = v.split(':')[0]
            violationCounts[type] = (violationCounts[type] || 0) + 1
          })

          console.log('[🚨 PATH-AUDIT] Violation breakdown:')
          Object.entries(violationCounts).forEach(([type, count]) => {
            console.log(`[🚨 PATH-AUDIT]   ${type}: ${count} occurrences`)
          })

          // Show recent violations
          const recentViolations = (window as any).pathAuditResults
            .filter((r: any) => r.violations.length > 0)
            .slice(-5)

          if (recentViolations.length > 0) {
            console.log('[🚨 PATH-AUDIT] Recent violations:')
            recentViolations.forEach((r: any) => {
              console.log(`[🚨 PATH-AUDIT]   ${r.operation}: ${r.violations.join(', ')}`)
              console.log(`[🚨 PATH-AUDIT]     Path: ${r.filePath}`)
            })
          }

          return {
            totalOperations: (window as any).pathAuditResults.length,
            totalViolations: allViolations.length,
            totalWarnings: allWarnings.length,
            violationBreakdown: violationCounts,
            recentViolations
          }
        }
      }
    }
  }

  /**
   * Generate hash with old algorithm (no normalization)
   */
  private generateHashWithOldAlgorithm(filePath: string): string {
    let hash = 0
    for (let i = 0; i < filePath.length; i++) {
      const char = filePath.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash
    }
    const fileName = filePath.split(/[/\\]/).pop()?.replace(/\.[^.]*$/, '') || 'unknown'
    const hashSuffix = Math.abs(hash).toString(16).substring(0, 8)
    return `${fileName}_${hashSuffix}`
  }

  /**
   * Store vault-level intelligence aggregation
   */
  async storeVaultIntelligence(
    vaultPath: string, 
    intelligence: VaultIntelligence
  ): Promise<void> {
    return await this.executeOperationOrThrow(
      'storeVaultIntelligence',
      async () => {
        this.logger.info('Storing vault intelligence', 'storeVaultIntelligence', { vaultPath })

        const contextDir = this.getContextDirectory(vaultPath)
        const intelligenceFile = joinLike(contextDir, this.VAULT_INTELLIGENCE_FILE)

        // Ensure directory exists
        await this.ensureDirectoryExists(contextDir)

        // Prepare intelligence data with metadata
        const intelligenceData = {
          ...intelligence,
          storage_metadata: {
            vault_path: vaultPath,
            stored_at: new Date().toISOString(),
            storage_version: '1.0'
          }
        }

        // Write to file
        await this.writeJsonFile(intelligenceFile, intelligenceData)

        this.logger.info('Vault intelligence stored successfully', 'storeVaultIntelligence', {
          vaultPath,
          totalFiles: intelligence.total_files_processed,
          totalIdeas: intelligence.aggregated_ideas.length
        })
      },
      { vaultPath }
    )
  }

  /**
   * Retrieve vault intelligence data
   */
  async getVaultIntelligence(vaultPath: string): Promise<VaultIntelligence | null> {
    return await this.executeOperationOrThrow(
      'getVaultIntelligence',
      async () => {
        const contextDir = this.getContextDirectory(vaultPath)
        const intelligenceFile = joinLike(contextDir, this.VAULT_INTELLIGENCE_FILE)

        try {
          const data = await this.readJsonFile(intelligenceFile)
          
          if (data && this.isValidVaultIntelligence(data)) {
            this.logger.info('Vault intelligence retrieved', 'getVaultIntelligence', { vaultPath })
            return data as VaultIntelligence
          }

          return null
        } catch (error) {
          this.logger.debug('Vault intelligence not found', 'getVaultIntelligence', { vaultPath })
          return null
        }
      },
      { vaultPath }
    )
  }

  /**
   * Update vault intelligence incrementally
   */
  async updateVaultIntelligence(
    vaultPath: string, 
    newFileIntelligence: FileIntelligence,
    filePath: string
  ): Promise<void> {
    return await this.executeOperationOrThrow(
      'updateVaultIntelligence',
      async () => {
        // Get existing vault intelligence
        let vaultIntelligence = await this.getVaultIntelligence(vaultPath)

        if (!vaultIntelligence) {
          // Create new vault intelligence
          vaultIntelligence = {
            vault_path: vaultPath,
            total_files_processed: 0,
            last_updated: new Date().toISOString(),
            aggregated_ideas: [],
            top_entities: [],
            human_connections: [],
            processing_summary: {
              total_processing_time_ms: 0,
              average_confidence: 0,
              models_used: [],
              last_full_scan: new Date().toISOString()
            }
          }
        }

        // Update with new file data
        vaultIntelligence.total_files_processed += 1
        vaultIntelligence.last_updated = new Date().toISOString()

        // Merge key ideas (avoid duplicates)
        const existingIdeaTexts = new Set(vaultIntelligence.aggregated_ideas.map(idea => idea.text.toLowerCase()))
        const newIdeas = newFileIntelligence.key_ideas.filter(idea => 
          !existingIdeaTexts.has(idea.text.toLowerCase())
        )
        vaultIntelligence.aggregated_ideas.push(...newIdeas)

        // Sort by relevance and keep top 50
        vaultIntelligence.aggregated_ideas.sort((a, b) => b.relevance_score - a.relevance_score)
        vaultIntelligence.aggregated_ideas = vaultIntelligence.aggregated_ideas.slice(0, 50)

        // Merge entities (avoid duplicates)
        const existingEntityTexts = new Set(vaultIntelligence.top_entities.map(entity => entity.text.toLowerCase()))
        const newEntities = newFileIntelligence.weighted_entities.filter(entity => 
          !existingEntityTexts.has(entity.text.toLowerCase())
        )
        vaultIntelligence.top_entities.push(...newEntities)

        // Sort by weight and keep top 30
        vaultIntelligence.top_entities.sort((a, b) => b.weight - a.weight)
        vaultIntelligence.top_entities = vaultIntelligence.top_entities.slice(0, 30)

        // Merge human connections (avoid duplicates)
        const existingConnections = new Set(vaultIntelligence.human_connections.map(conn => 
          `${conn.name?.toLowerCase()}_${conn.email?.toLowerCase()}_${conn.company?.toLowerCase()}`
        ))
        const newConnections = newFileIntelligence.human_connections.filter(conn => {
          const key = `${conn.name?.toLowerCase()}_${conn.email?.toLowerCase()}_${conn.company?.toLowerCase()}`
          return !existingConnections.has(key)
        })
        vaultIntelligence.human_connections.push(...newConnections)

        // Sort by confidence and keep top 20
        vaultIntelligence.human_connections.sort((a, b) => b.connection_strength - a.connection_strength)
        vaultIntelligence.human_connections = vaultIntelligence.human_connections.slice(0, 20)

        // Update processing summary
        vaultIntelligence.processing_summary.total_processing_time_ms += 
          newFileIntelligence.analysis_metadata.processing_time_ms

        const modelUsed = newFileIntelligence.analysis_metadata.model_used
        if (modelUsed && !vaultIntelligence.processing_summary.models_used.includes(modelUsed)) {
          vaultIntelligence.processing_summary.models_used.push(modelUsed)
        }

        // Recalculate average confidence
        vaultIntelligence.processing_summary.average_confidence = 
          (vaultIntelligence.processing_summary.average_confidence * (vaultIntelligence.total_files_processed - 1) + 
           newFileIntelligence.processing_confidence) / vaultIntelligence.total_files_processed

        // Store updated vault intelligence
        await this.storeVaultIntelligence(vaultPath, vaultIntelligence)

        this.logger.info('Vault intelligence updated', 'updateVaultIntelligence', {
          vaultPath,
          filePath,
          totalFiles: vaultIntelligence.total_files_processed,
          totalIdeas: vaultIntelligence.aggregated_ideas.length
        })
      },
      { vaultPath, filePath }
    )
  }

  /**
   * Generate file hash for unique identification (deterministic)
   */
  private generateFileHash(filePath: string): string {
    console.log('[LABELS] 🔑 generateFileHash called with filePath:', filePath)

    // NORMALIZE the file path to ensure consistent hashing
    // Convert to forward slashes and lowercase for consistent hashing
    const normalizedPath = filePath.replace(/\\/g, '/').toLowerCase()
    console.log('[LABELS] 🔑 Normalized path for hashing:', normalizedPath)

    // Create deterministic hash based only on normalized file path (no timestamp)
    // This ensures the same file always gets the same hash
    let hash = 0
    for (let i = 0; i < normalizedPath.length; i++) {
      const char = normalizedPath.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }

    // Create a more readable filename from the original path
    const fileName = filePath.split(/[/\\]/).pop()?.replace(/\.[^.]*$/, '') || 'unknown'
    const hashSuffix = Math.abs(hash).toString(16).substring(0, 8)
    const result = `${fileName}_${hashSuffix}`

    console.log('[LABELS] 🔑 File hash generation:')
    console.log('[LABELS] 🔑   Original filePath:', filePath)
    console.log('[LABELS] 🔑   Normalized path:', normalizedPath)
    console.log('[LABELS] 🔑   Extracted fileName:', fileName)
    console.log('[LABELS] 🔑   Raw hash:', hash)
    console.log('[LABELS] 🔑   Hash suffix:', hashSuffix)
    console.log('[LABELS] 🔑   Final hash:', result)

    return result
  }

  /**
   * Get context directory path using proper path joining
   */
  private getContextDirectory(vaultPath: string): string {
    return joinLike(vaultPath, this.CONTEXT_DIR)
  }

  /**
   * Get files directory path using proper path joining
   */
  private getFilesDirectory(vaultPath: string): string {
    return joinLike(vaultPath, this.CONTEXT_DIR, this.FILES_DIR)
  }

  /**
   * Ensure directory exists
   */
  private async ensureDirectoryExists(dirPath: string): Promise<void> {
    try {
      if (window.electronAPI?.vault?.createDirectory) {
        const result = await window.electronAPI.vault.createDirectory(dirPath)
        if (!result.success) {
          throw new Error(`Failed to create directory: ${result.error}`)
        }
      }
    } catch (error) {
      this.logger.error('Failed to ensure directory exists', 'ensureDirectoryExists', { dirPath, error })
      throw error
    }
  }

  /**
   * Write JSON data to file
   */
  private async writeJsonFile(filePath: string, data: any): Promise<void> {
    try {
      console.log('[LABELS] 💾 writeJsonFile: Starting write operation')
      console.log('[LABELS] 💾 writeJsonFile: Target path:', filePath)
      console.log('[LABELS] 💾 writeJsonFile: electronAPI available:', !!window.electronAPI?.vault?.writeFile)

      if (window.electronAPI?.vault?.writeFile) {
        // Use proper UTF-8 encoding for international characters
        const jsonContent = JSON.stringify(data, null, 2)
          .replace(/\\u[\dA-F]{4}/gi, (match) => {
            return String.fromCharCode(parseInt(match.replace(/\\u/g, ''), 16))
          })

        console.log('[LABELS] 💾 writeJsonFile: JSON content length:', jsonContent.length)
        console.log('[LABELS] 💾 writeJsonFile: Calling electronAPI.vault.writeFile...')

        const result = await window.electronAPI.vault.writeFile(filePath, jsonContent)

        console.log('[LABELS] 💾 writeJsonFile: Write result:', result)

        if (!result.success) {
          console.error('[LABELS] 💾 writeJsonFile: Write failed with error:', result.error)
          throw new Error(`Failed to write file: ${result.error}`)
        }

        console.log('[LABELS] 💾 writeJsonFile: ✅ Write operation completed successfully')
        console.log('[LABELS] 💾 writeJsonFile: File has been written to disk and should be available')
      } else {
        console.error('[LABELS] 💾 writeJsonFile: electronAPI.vault.writeFile not available')
        throw new Error('electronAPI.vault.writeFile not available')
      }
    } catch (error) {
      console.error('[LABELS] 💾 writeJsonFile: Exception during write:', error)
      this.logger.error('Failed to write JSON file', 'writeJsonFile', { filePath, error })
      throw error
    }
  }

  /**
   * Read JSON data from file
   */
  private async readJsonFile(filePath: string): Promise<any> {
    try {
      if (window.electronAPI?.vault?.readFile) {
        const result = await window.electronAPI.vault.readFile(filePath)
        if (!result.success) {
          // File doesn't exist - return null instead of throwing
          if (result.error?.includes('ENOENT') || result.error?.includes('no such file')) {
            this.logger.info('JSON file does not exist', 'readJsonFile', { filePath })
            return null
          }
          throw new Error(`Failed to read file: ${result.error}`)
        }

        // Handle empty or invalid content
        if (!result.content || result.content.trim() === '') {
          this.logger.warn('JSON file is empty', 'readJsonFile', { filePath })
          return null
        }

        try {
          return JSON.parse(result.content)
        } catch (parseError) {
          this.logger.warn('Corrupted JSON file detected - auto-cleaning', 'readJsonFile', { filePath, parseError })
          // Auto-delete corrupted meta files since they can be regenerated
          await this.deleteCorruptedFile(filePath)
          return null
        }
      }
      return null
    } catch (error) {
      this.logger.error('Failed to read JSON file', 'readJsonFile', { filePath, error })
      throw error
    }
  }

  /**
   * Auto-delete corrupted meta files since they can be regenerated
   * ✅ FIXED: Now handles ENOENT errors gracefully for non-existent files
   */
  private async deleteCorruptedFile(filePath: string): Promise<void> {
    console.log('🗑️ [DELETE-FILE] Starting deleteCorruptedFile for:', filePath)

    try {
      // First check if file exists to avoid ENOENT errors
      console.log('🔍 [DELETE-FILE] Checking if file exists...')
      if (window.electronAPI?.vault?.pathExists) {
        const existsResult = await window.electronAPI.vault.pathExists(filePath)
        if (!existsResult.exists) {
          console.log('ℹ️ [DELETE-FILE] File does not exist, skipping deletion:', filePath)
          this.logger.info('File does not exist, skipping deletion', 'deleteCorruptedFile', { filePath })
          return
        }
      }

      console.log('🔧 [DELETE-FILE] Checking electronAPI availability...')
      if (window.electronAPI?.vault?.removeFile) {
        console.log('✅ [DELETE-FILE] electronAPI.vault.removeFile is available')
        console.log('🗑️ [DELETE-FILE] Attempting to remove file...')

        const result = await window.electronAPI.vault.removeFile(filePath)
        console.log('📊 [DELETE-FILE] Remove file result:', result)

        if (result.success) {
          console.log('✅ [DELETE-FILE] File removed successfully')
          this.logger.info('Corrupted meta file deleted successfully', 'deleteCorruptedFile', { filePath })
        } else {
          // Check if error is ENOENT (file not found) - this is not really an error
          if (result.error && (result.error.includes('ENOENT') || result.error.includes('no such file'))) {
            console.log('ℹ️ [DELETE-FILE] File already does not exist:', filePath)
            this.logger.info('File already does not exist', 'deleteCorruptedFile', { filePath })
          } else {
            console.log('⚠️ [DELETE-FILE] Remove file returned success=false:', result)
            this.logger.warn('Failed to delete corrupted file', 'deleteCorruptedFile', { filePath, error: result.error })
          }
        }
      } else {
        console.log('❌ [DELETE-FILE] electronAPI.vault.removeFile is NOT available')
        console.log('🔍 [DELETE-FILE] Available electronAPI methods:', {
          electronAPI: !!window.electronAPI,
          vault: !!window.electronAPI?.vault,
          removeFile: !!window.electronAPI?.vault?.removeFile,
          deleteFile: !!window.electronAPI?.vault?.deleteFile
        })
      }
    } catch (error) {
      console.error('💥 [DELETE-FILE] Error during file deletion:', error)
      this.logger.warn('Error during corrupted file cleanup', 'deleteCorruptedFile', { filePath, error })
      // Don't throw - cleanup is best effort
    }

    console.log('🏁 [DELETE-FILE] deleteCorruptedFile completed for:', filePath)
  }

  /**
   * Clear all intelligence meta files in a vault (nuclear option for corrupted state)
   */
  async clearVaultIntelligenceState(vaultPath: string): Promise<void> {
    console.log('[LABELS] 🗑️ CLEAR-SERVICE: Starting clearVaultIntelligenceState for:', vaultPath)

    return await this.executeOperationOrThrow(
      'clearVaultIntelligenceState',
      async () => {
        console.log('[LABELS] 🗑️ CLEAR-SERVICE: Getting context directory...')
        const contextDir = this.getContextDirectory(vaultPath)
        console.log('[LABELS] 🗑️ CLEAR-SERVICE: Context directory:', contextDir)
        console.log('[LABELS] 🗑️ CLEAR-SERVICE: CONTEXT_DIR constant:', this.CONTEXT_DIR)
        console.log('[LABELS] 🗑️ CLEAR-SERVICE: Full context path calculation:', `${vaultPath}/${this.CONTEXT_DIR}`)

        // Delete vault intelligence file
        const vaultIntelligenceFile = joinLike(contextDir, this.VAULT_INTELLIGENCE_FILE)
        console.log('🎯 [CLEAR-SERVICE] Target vault intelligence file:', vaultIntelligenceFile)

        console.log('🗑️ [CLEAR-SERVICE] Attempting to delete vault intelligence file...')
        await this.deleteCorruptedFile(vaultIntelligenceFile)
        console.log('✅ [CLEAR-SERVICE] Vault intelligence file deletion completed')

        // Delete files directory if it exists
        const filesDir = joinLike(contextDir, this.FILES_DIR)
        console.log('[LABELS] 🗑️ CLEAR-SERVICE: Target files directory:', filesDir)
        console.log('[LABELS] 🗑️ CLEAR-SERVICE: FILES_DIR constant:', this.FILES_DIR)
        console.log('[LABELS] 🗑️ CLEAR-SERVICE: Full files path calculation:', joinLike(contextDir, this.FILES_DIR))

        try {
          console.log('🔧 [CLEAR-SERVICE] Checking electronAPI availability...')
          if (window.electronAPI?.vault?.removeDirectory) {
            console.log('✅ [CLEAR-SERVICE] electronAPI.vault.removeDirectory is available')
            console.log('🗑️ [CLEAR-SERVICE] Attempting to remove files directory...')

            const result = await window.electronAPI.vault.removeDirectory(filesDir)
            console.log('📊 [CLEAR-SERVICE] Remove directory result:', result)

            if (result.success) {
              console.log('✅ [CLEAR-SERVICE] Files directory removed successfully')
              this.logger.info('Cleared files intelligence directory', 'clearVaultIntelligenceState', { filesDir })
            } else {
              console.log('⚠️ [CLEAR-SERVICE] Remove directory returned success=false:', result)
            }
          } else {
            console.log('❌ [CLEAR-SERVICE] electronAPI.vault.removeDirectory is NOT available')
            console.log('🔍 [CLEAR-SERVICE] Available electronAPI methods:', {
              electronAPI: !!window.electronAPI,
              vault: !!window.electronAPI?.vault,
              removeDirectory: !!window.electronAPI?.vault?.removeDirectory,
              deleteDirectory: !!window.electronAPI?.vault?.deleteDirectory
            })
          }
        } catch (error) {
          console.error('💥 [CLEAR-SERVICE] Error during files directory cleanup:', error)
          this.logger.debug('Files directory cleanup skipped', 'clearVaultIntelligenceState', { filesDir, error })
        }

        console.log('🎉 [CLEAR-SERVICE] Vault intelligence state clearing completed')
        this.logger.info('Vault intelligence state cleared', 'clearVaultIntelligenceState', { vaultPath })
      },
      { vaultPath }
    )
  }

  /**
   * Validate file intelligence data structure
   */
  private isValidFileIntelligence(data: any): boolean {
    return data && 
           Array.isArray(data.key_ideas) &&
           Array.isArray(data.weighted_entities) &&
           Array.isArray(data.human_connections) &&
           typeof data.processing_confidence === 'number' &&
           data.analysis_metadata
  }

  /**
   * Validate vault intelligence data structure
   */
  private isValidVaultIntelligence(data: any): boolean {
    return data &&
           typeof data.vault_path === 'string' &&
           typeof data.total_files_processed === 'number' &&
           Array.isArray(data.aggregated_ideas) &&
           Array.isArray(data.top_entities) &&
           Array.isArray(data.human_connections) &&
           data.processing_summary
  }
}

export const intelligenceStorageService = new IntelligenceStorageService()
