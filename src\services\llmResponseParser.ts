/**
 * LLM Response Parser Service
 * 
 * A clean, new service to handle LLM response parsing without the baggage of old logic.
 * Specifically designed to handle the new format:
 * - Project Management ; score=99; intents=topic,action; context=long-term strategy; entities=Agile,Scrum
 */

import { KeyIdea } from '../types/fileIntelligenceTypes'

export interface ParsedLLMResponse {
  success: boolean
  key_ideas: KeyIdea[]
  error?: string
  debug_info?: {
    format_detected: string
    lines_processed: number
    raw_response_length: number
    has_file_intel_block: boolean
    has_end_marker: boolean
  }
}

export class LLMResponseParser {
  
  /**
   * Main entry point - parse any LLM response format
   */
  public parseResponse(response: string): ParsedLLMResponse {
    console.log('[LLM-PARSER] 🚀 Starting LLM response parsing...')
    console.log('[LLM-PARSER] 📝 Response length:', response.length)
    
    try {
      // Try new FILE_INTEL format first
      const fileIntelResult = this.parseFileIntelFormat(response)
      if (fileIntelResult.success) {
        console.log('[LLM-PARSER] ✅ Successfully parsed FILE_INTEL format')
        return fileIntelResult
      }
      
      // Try JSON format as fallback
      const jsonResult = this.parseJsonFormat(response)
      if (jsonResult.success) {
        console.log('[LLM-PARSER] ✅ Successfully parsed JSON format')
        return jsonResult
      }
      
      // If both fail, return error
      return {
        success: false,
        key_ideas: [],
        error: 'No supported format detected in LLM response',
        debug_info: {
          format_detected: 'unknown',
          lines_processed: 0,
          raw_response_length: response.length,
          has_file_intel_block: response.includes('!-- FILE_INTEL:BEGIN --'),
          has_end_marker: response.includes('!-- FILE_INTEL:END --')
        }
      }
      
    } catch (error) {
      console.error('[LLM-PARSER] ❌ Parsing failed:', error)
      return {
        success: false,
        key_ideas: [],
        error: error instanceof Error ? error.message : 'Unknown parsing error'
      }
    }
  }
  
  /**
   * Parse FILE_INTEL format (new robust logic)
   */
  private parseFileIntelFormat(response: string): ParsedLLMResponse {
    console.log('[LLM-PARSER] 🔍 Attempting FILE_INTEL format parsing...')
    
    // Look for FILE_INTEL block - be more flexible about end marker
    const startMarker = '!-- FILE_INTEL:BEGIN --'
    const endMarker = '!-- FILE_INTEL:END --'
    
    const startIdx = response.indexOf(startMarker)
    if (startIdx === -1) {
      console.log('[LLM-PARSER] ❌ No FILE_INTEL:BEGIN marker found')
      return { success: false, key_ideas: [] }
    }
    
    let endIdx = response.indexOf(endMarker, startIdx + startMarker.length)
    let hasEndMarker = true
    
    // If no end marker, try to find a reasonable end point
    if (endIdx === -1) {
      console.log('[LLM-PARSER] ⚠️ No FILE_INTEL:END marker found, looking for alternative end points...')
      hasEndMarker = false
      
      // Look for common end patterns
      const possibleEnds = [
        response.indexOf('```', startIdx + startMarker.length),
        response.indexOf('\n\n---', startIdx + startMarker.length),
        response.indexOf('\n\n#', startIdx + startMarker.length),
        response.length // Use entire response as fallback
      ].filter(idx => idx !== -1)
      
      endIdx = Math.min(...possibleEnds)
      console.log('[LLM-PARSER] 🔧 Using alternative end point at index:', endIdx)
    }
    
    // Extract the content
    const content = response.substring(startIdx + startMarker.length, endIdx)
    console.log('[LLM-PARSER] 📄 Extracted content length:', content.length)
    
    // Clean and normalize the content
    const cleanContent = content
      .replace(/```[a-zA-Z]*\n?/g, '') // Remove code fence markers
      .replace(/```/g, '')
      .replace(/\r/g, '')
      .trim()
    
    // Extract lines that look like ideas
    const lines = cleanContent.split('\n').map(l => l.trim()).filter(Boolean)
    console.log('[LLM-PARSER] 📋 Total lines found:', lines.length)
    
    const ideas: KeyIdea[] = []
    let linesProcessed = 0
    
    for (const line of lines) {
      // Skip header lines
      if (line.startsWith('#') || line.startsWith('##') || line.startsWith('Key Ideas')) {
        continue
      }
      
      // Process lines that start with "- "
      if (line.startsWith('- ')) {
        const idea = this.parseIdeaLine(line, ideas.length)
        if (idea) {
          ideas.push(idea)
          linesProcessed++
          console.log('[LLM-PARSER] ✅ Parsed idea:', idea.text, '(score:', idea.relevance_score + ')')
        }
      }
    }
    
    console.log('[LLM-PARSER] 🎯 FILE_INTEL parsing complete:', ideas.length, 'ideas extracted')
    
    return {
      success: ideas.length > 0,
      key_ideas: ideas,
      debug_info: {
        format_detected: 'file_intel',
        lines_processed: linesProcessed,
        raw_response_length: response.length,
        has_file_intel_block: true,
        has_end_marker: hasEndMarker
      }
    }
  }
  
  /**
   * Parse a single idea line in the new format
   * Format: "- Project Management ; score=99; intents=topic,action; context=long-term strategy; entities=Agile,Scrum"
   */
  private parseIdeaLine(line: string, index: number): KeyIdea | null {
    try {
      console.log('[LLM-PARSER] 🔍 Parsing line:', line)
      
      // Remove leading "- " and handle quoted text
      let cleanLine = line.replace(/^-\s*/, '').trim()
      
      // Handle quoted text at the beginning
      let text = ''
      let remainingParts = ''
      
      if (cleanLine.startsWith('"')) {
        // Find the closing quote
        const closeQuoteIdx = cleanLine.indexOf('"', 1)
        if (closeQuoteIdx !== -1) {
          text = cleanLine.substring(1, closeQuoteIdx)
          remainingParts = cleanLine.substring(closeQuoteIdx + 1).trim()
          // Remove leading " ; " if present
          if (remainingParts.startsWith(' ; ')) {
            remainingParts = remainingParts.substring(3)
          }
        } else {
          // Malformed quoted text, try without quotes
          const parts = cleanLine.split(' ; ')
          text = parts[0].replace(/^"/, '').trim()
          remainingParts = parts.slice(1).join(' ; ')
        }
      } else {
        // No quotes, split by " ; "
        const parts = cleanLine.split(' ; ')
        text = parts[0].trim()
        remainingParts = parts.slice(1).join(' ; ')
      }
      
      if (!text) {
        console.log('[LLM-PARSER] ⚠️ No text found in line')
        return null
      }
      
      // Parse key=value pairs from remaining parts
      const pairs: Record<string, string> = {}
      if (remainingParts) {
        const keyValueParts = remainingParts.split(';').map(p => p.trim())
        
        for (const part of keyValueParts) {
          const equalIdx = part.indexOf('=')
          if (equalIdx > 0) {
            const key = part.substring(0, equalIdx).trim()
            const value = part.substring(equalIdx + 1).trim()
            pairs[key] = value
          }
        }
      }
      
      console.log('[LLM-PARSER] 📊 Extracted:', { text, pairs })
      
      // Create KeyIdea object
      const idea: KeyIdea = {
        id: `idea_${Date.now()}_${index}`,
        text: text,
        relevance_score: parseInt(pairs.score) || 80,
        intent_types: pairs.intents ? pairs.intents.split(',').map(s => s.trim()) as any[] : ['topic'],
        context: pairs.context || '',
        auto_selected: parseInt(pairs.score) >= 90,
        user_confirmed: false,
        weight: parseInt(pairs.score) || 80
      }
      
      return idea
      
    } catch (error) {
      console.error('[LLM-PARSER] ❌ Failed to parse line:', line, error)
      return null
    }
  }
  
  /**
   * Parse JSON format (fallback)
   */
  private parseJsonFormat(response: string): ParsedLLMResponse {
    console.log('[LLM-PARSER] 🔍 Attempting JSON format parsing...')
    
    // Look for JSON in code blocks or raw JSON
    const jsonMatches = [
      response.match(/```json\s*([\s\S]*?)\s*```/i),
      response.match(/```\s*([\s\S]*?)\s*```/),
      response.match(/\{[\s\S]*\}/)
    ].filter(Boolean)
    
    for (const match of jsonMatches) {
      if (match && match[1]) {
        try {
          const parsed = JSON.parse(match[1])
          if (parsed.key_ideas && Array.isArray(parsed.key_ideas)) {
            console.log('[LLM-PARSER] ✅ Found valid JSON with key_ideas')
            return {
              success: true,
              key_ideas: parsed.key_ideas,
              debug_info: {
                format_detected: 'json',
                lines_processed: parsed.key_ideas.length,
                raw_response_length: response.length,
                has_file_intel_block: false,
                has_end_marker: false
              }
            }
          }
        } catch (error) {
          console.log('[LLM-PARSER] ⚠️ JSON parsing failed for match:', error)
          continue
        }
      }
    }
    
    console.log('[LLM-PARSER] ❌ No valid JSON format found')
    return { success: false, key_ideas: [] }
  }
}

// Export singleton instance
export const llmResponseParser = new LLMResponseParser()
