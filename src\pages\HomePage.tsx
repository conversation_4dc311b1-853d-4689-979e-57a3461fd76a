import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useNavigation } from '../hooks/useNavigation'



import { ContextVaultCard } from '../types'
import { vaultUIManager } from '../services/vaultUIManager'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { ICONS } from '../components/Icons/index'

import { useArtifactToasts } from '../components/artifacts/controls/ArtifactToast'
import VaultContextOverview from '../components/VaultContextOverview'
import { batchFileProcessingService } from '../services/batchFileProcessingService'
import { BatchProcessingStatus } from '../types/fileIntelligenceTypes'
import PerformanceMonitorPage from './PerformanceMonitorPage'


const HomePage: React.FC = () => {
  const navigate = useNavigate()
  const toasts = useArtifactToasts()
  const [searchQuery, setSearchQuery] = useState('')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [sortBy] = useState('Recent')
  const [selectedContext, setSelectedContext] = useState<ContextVaultCard | null>(null)
  const [contexts, setContexts] = useState<ContextVaultCard[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [dragOver, setDragOver] = useState<string | null>(null)
  const [isOrganizing, setIsOrganizing] = useState(false)
  const [organizingProgress, setOrganizingProgress] = useState<BatchProcessingStatus | null>(null)
  const [showPerformanceMonitor, setShowPerformanceMonitor] = useState(false)

  // Load vault data on component mount
  useEffect(() => {
    loadVaultData()
  }, [])

  // REMOVED: Document-level drag/drop handlers that were causing flickering
  // Let the browser handle default behavior and only intercept at card level

  const loadVaultData = async () => {
    try {
      setLoading(true)
      setError(null)

      // Check if vault registry exists
      const registry = await vaultUIManager.getVaultRegistry()

      if (!registry) {
        // No vault setup yet - show empty state
        setContexts([])
        setLoading(false)
        return
      }

      // Load vault cards
      const cards = await vaultUIManager.getVaultCards()
      setContexts(cards)
    } catch (err: any) {
      console.error('Error loading vault data:', err)
      setError(err.message || 'Failed to load vault data')
    } finally {
      setLoading(false)
    }
  }

  const handleClearState = async () => {
    console.log('🗑️ [CLEAR-DEBUG] Clear button clicked - Starting intelligence cleanup process')

    try {
      // Import the services dynamically
      console.log('🔧 [CLEAR-DEBUG] Importing services...')
      const { storageServiceAdapter } = await import('../services/storageServiceAdapter')
      const { vaultUIManager } = await import('../services/vaultUIManager')
      console.log('✅ [CLEAR-DEBUG] Services imported successfully')

      // Get vault paths dynamically from user settings
      console.log('🔍 [CLEAR-DEBUG] Loading vault registry to get actual vault paths...')
      const vaultRegistry = await vaultUIManager.getVaultRegistry()

      if (!vaultRegistry || !vaultRegistry.vaults || vaultRegistry.vaults.length === 0) {
        console.warn('⚠️ [CLEAR-DEBUG] No vaults found in registry')
        toasts.info('No vaults found - Please configure vaults in Settings first')
        return
      }

      // Extract vault paths from registry
      const vaultPaths = vaultRegistry.vaults.map(vault => vault.path)
      console.log('🎯 [CLEAR-DEBUG] Target vaults for cleanup (from registry):', vaultPaths)

      for (let i = 0; i < vaultPaths.length; i++) {
        const vaultPath = vaultPaths[i]
        console.log(`🚀 [CLEAR-DEBUG] Processing vault ${i + 1}/${vaultPaths.length}: ${vaultPath}`)

        try {
          await storageServiceAdapter.clearVaultIntelligenceState(vaultPath)
          console.log(`✅ [CLEAR-DEBUG] Vault ${i + 1} cleared successfully: ${vaultPath}`)
        } catch (vaultError) {
          console.error(`❌ [CLEAR-DEBUG] Failed to clear vault ${i + 1}: ${vaultPath}`, vaultError)
          // Continue with other vaults instead of throwing
          console.warn(`⚠️ [CLEAR-DEBUG] Continuing with remaining vaults...`)
        }
      }

      console.log('🎉 [CLEAR-DEBUG] Vault intelligence cleanup completed!')

      // STEP 2: Force vault registry regeneration from current settings
      console.log('🔄 [CLEAR-DEBUG] Starting vault registry regeneration...')
      try {
        // Import services
        const { contextVaultService } = await import('../services/contextVaultService')

        // Get current vault root from settings to regenerate registry
        console.log('🔍 [CLEAR-DEBUG] Getting current vault root from settings...')
        const currentVaultRoot = await window.electronAPI.settings.get('vault-root-path')
        console.log('📁 [CLEAR-DEBUG] Current vault root from settings:', currentVaultRoot)

        if (currentVaultRoot) {
          // Force regenerate vault registry by scanning current vault structure
          console.log('🔄 [CLEAR-DEBUG] Forcing vault registry regeneration from current structure...')
          const { vaultUIManager } = await import('../services/vaultUIManager')

          // This will scan the current vault structure and rebuild the registry
          const newRegistry = await vaultUIManager.scanVaults()
          console.log('📊 [CLEAR-DEBUG] New registry generated:', newRegistry)

          // Reload the context vault service with fresh data
          await contextVaultService.loadVaults()

          console.log('✅ [CLEAR-DEBUG] Vault registry regenerated successfully!')
          toasts.success(`Complete Reset - Intelligence cleared and vault registry regenerated from current settings`)
        } else {
          console.warn('⚠️ [CLEAR-DEBUG] No vault root found in settings')
          toasts.info('Intelligence cleared but no vault root configured - Please check Settings')
        }

      } catch (registryError) {
        console.error('❌ [CLEAR-DEBUG] Failed to regenerate vault registry:', registryError)
        toasts.info('Intelligence cleared but vault registry regeneration failed - Check console')
      }

    } catch (error) {
      console.error('💥 [CLEAR-DEBUG] State cleanup failed with error:', error)
      console.error('💥 [CLEAR-DEBUG] Error details:', {
        message: error?.message,
        stack: error?.stack,
        name: error?.name
      })
      toasts.error('Cleanup Failed - Check console for details')
    }
  }

  const handleNewChat = () => {
    navigate('/chat')
  }

  const handleOrganize = async () => {
    if (isOrganizing) return

    try {
      setIsOrganizing(true)
      setOrganizingProgress(null)

      // Show initial toast
      toasts.info('Starting AI Organization')

      // Listen for progress updates
      const progressListener = (progress: BatchProcessingStatus) => {
        setOrganizingProgress(progress)
      }

      // Event listener for progress updates (simplified for now)

      // Start processing all vaults using queue system
      const taskIds = await batchFileProcessingService.processAllVaultsWithQueue({
        min_ideas_required: 10,
        auto_select_top_n: 3,
        enable_human_priority: true
      })

      // Show success message for queued processing
      toasts.success(`${taskIds.length} files queued for processing. Check Performance Monitor for progress.`)

      // Clean up (simplified for now)

      // Reload vault data to show updated intelligence
      await loadVaultData()

    } catch (error) {
      console.error('Organization failed:', error)
      toasts.error('Organization Failed')
    } finally {
      setIsOrganizing(false)
      setOrganizingProgress(null)
    }
  }

  // Generate unique filename to avoid overwriting existing files
  const generateUniqueFilename = async (basePath: string, originalFilename: string): Promise<string> => {
    // Sanitize filename
    const preservedName = originalFilename
      .replace(/[<>:"/\\|?*]/g, '_')  // Only OS-forbidden chars
      .replace(/\s+/g, ' ')          // Normalize spaces
      .trim()                        // Remove leading/trailing spaces
      .substring(0, 200)             // Reasonable length limit

    // Check if file exists
    const fullPath = `${basePath}/${preservedName}`
    const exists = await window.electronAPI.vault.pathExists(fullPath)

    if (!exists.exists) {
      // No duplicate, use original name
      return preservedName
    }

    // File exists, generate numbered version
    const lastDotIndex = preservedName.lastIndexOf('.')
    const nameWithoutExt = lastDotIndex > 0 ? preservedName.substring(0, lastDotIndex) : preservedName
    const extension = lastDotIndex > 0 ? preservedName.substring(lastDotIndex) : ''

    let counter = 1
    let uniqueName: string

    do {
      uniqueName = `${nameWithoutExt} (${counter})${extension}`
      const testPath = `${basePath}/${uniqueName}`
      const testExists = await window.electronAPI.vault.pathExists(testPath)

      if (!testExists.exists) {
        break
      }
      counter++
    } while (counter < 1000) // Safety limit

    return uniqueName
  }

  // Handle click to open system file dialog
  const handleDropZoneClick = async (contextId: string) => {
    // Find the target context
    const targetContext = contexts.find(c => c.id === contextId)
    if (!targetContext) return

    try {
      // Use system file dialog to select files
      if (window.electronAPI?.files?.showOpenDialog) {
        const result = await window.electronAPI.files.showOpenDialog({
          title: 'Select Files to Upload',
          properties: ['openFile', 'multiSelections'],
          filters: [
            { name: 'All Files', extensions: ['*'] },
            { name: 'Documents', extensions: ['pdf', 'doc', 'docx', 'txt', 'md'] },
            { name: 'Images', extensions: ['jpg', 'jpeg', 'png', 'gif', 'webp'] },
            { name: 'Archives', extensions: ['zip', 'rar', '7z'] }
          ]
        })

        if (!result.canceled && result.filePaths && result.filePaths.length > 0) {
          // Copy files directly to destination using vault.copyFile
          let successCount = 0
          let errorCount = 0

          // Determine destination path
          const destinationBasePath = targetContext.path + '/documents'

          for (const filePath of result.filePaths) {
            try {
              const fileName = filePath.split(/[/\\]/).pop() || 'unknown'

              // Generate unique filename to avoid overwriting
              const uniqueFilename = await generateUniqueFilename(destinationBasePath, fileName)
              const destinationPath = `${destinationBasePath}/${uniqueFilename}`

              // Copy file directly using vault API
              const copyResult = await window.electronAPI.vault.copyFile(filePath, destinationPath)

              if (copyResult.success) {
                successCount++
                console.log(`✅ Copied: ${fileName} → ${uniqueFilename}`)
              } else {
                errorCount++
                console.error(`❌ Failed to copy: ${fileName}`, copyResult.error)
                toasts.error(`Failed to copy: ${fileName}`)
              }
            } catch (error) {
              errorCount++
              console.error(`Error copying file ${filePath}:`, error)
              toasts.error(`Error copying file: ${filePath.split(/[/\\]/).pop()}`)
            }
          }

          // Show summary toast
          if (successCount > 0) {
            toasts.success(`✅ ${successCount} file(s) copied successfully!`)
          }
          if (errorCount > 0) {
            toasts.error(`❌ ${errorCount} file(s) failed to copy`)
          }
        }
      } else {
        // Fallback: Show guidance to manually copy files
        toasts.info(`📁 Please manually copy files to your "${targetContext.name}" context vault folder`)

        // Try to open the vault folder (if available)
        if (targetContext.path) {
          toasts.info(`📂 Vault folder: ${targetContext.path}`)
        }
      }
    } catch (error) {
      console.error('Error in file selection:', error)
      toasts.error('Error selecting files')
    }
  }

  // Process files with size check and routing logic
  const processFilesForContext = async (files: File[], context: ContextVaultCard) => {
    const STREAMING_THRESHOLD = 9 * 1024 * 1024 // 9MB

    for (const file of files) {
      const fileSizeMB = file.size / 1024 / 1024
      const isLargeFile = file.size > STREAMING_THRESHOLD

      if (isLargeFile) {
        // Large file: Show guidance to manually copy to vault folder
        console.log(`📁 Large file detected (${fileSizeMB.toFixed(1)}MB): ${file.name}`)

        // Show guidance toast for large files
        const folderName = context.name
        toasts.info(`📁 File too large (${fileSizeMB.toFixed(1)}MB) - Please manually copy "${file.name}" to your "${folderName}" context vault folder`)

        // Show vault path for user reference
        if (context.path) {
          toasts.info(`📂 Vault folder: ${context.path}`)
        }
        return
      } else {
        // Small file: Use streaming upload
        console.log(`🌊 Small file detected (${fileSizeMB.toFixed(1)}MB): ${file.name} - Using streaming upload`)
        await handleStreamingUpload([file], context)
      }
    }
  }

  // Handle streaming upload for small files
  const handleStreamingUpload = async (files: File[], context: ContextVaultCard) => {
    try {
      // Dynamic import to avoid circular dependencies
      const { vaultFileHandler } = await import('../services/vaultFileHandler')

      // Create upload destination for this context
      const destination = {
        type: 'context' as const,
        path: context.path || '',
        contextId: context.id,
        contextName: context.name
      }

      // Upload files using the streaming handler
      const results = await vaultFileHandler.replaceSharedDropboxService(
        files,
        destination,
        (fileName, progress) => {
          if (process.env.NODE_ENV === 'development') {
            console.log(`📊 [STREAMING] Upload progress for ${fileName}:`, {
              percentage: progress.percentage.toFixed(1),
              method: progress.totalChunks > 1 ? 'streaming' : 'batch',
              chunk: `${progress.currentChunk}/${progress.totalChunks}`,
              speed: progress.speed > 0 ? `${(progress.speed / 1024 / 1024).toFixed(1)} MB/s` : 'calculating...'
            })
          }
        }
      )

      // Show success notification
      const successful = results.filter(r => r.success).length
      if (successful > 0) {
        toasts.success(`✅ ${successful} file(s) uploaded successfully to "${context.name}"!`)

        // Trigger file intelligence processing for uploaded files
        try {
          const successfulResults = results.filter(r => r.success)
          for (const result of successfulResults) {
            if (result.filePath) {
              // Process file intelligence in background
              batchFileProcessingService.processVault({
                vaultPath: context.path || '',
                vaultName: context.name,
                maxFiles: 1, // Process only the newly uploaded file
                config: {
                  min_ideas_required: 8,
                  auto_select_top_n: 3,
                  enable_human_priority: true
                }
              }).catch(error => {
                console.warn('File intelligence processing failed for uploaded file:', error)
              })
            }
          }
        } catch (error) {
          console.warn('Failed to trigger file intelligence processing:', error)
        }
      }

      // Refresh vault data
      await loadVaultData()
    } catch (error) {
      console.error('❌ [STREAMING] Error uploading files:', error)
      toasts.error('Error uploading files')
    }
  }

  const handleContextSelect = (context: ContextVaultCard) => {
    setSelectedContext(context)
  }



  const { navigateToChat, navigateToFile } = useNavigation()
  const [showUniversalSearch, setShowUniversalSearch] = useState(false)

  const handleContextChat = (contextId: string) => {
    navigateToChat(undefined, contextId)
  }

  const handleViewAllFiles = () => {
    navigateToFile(selectedContext?.id || '', '')
  }

  const closeContextDetails = () => {
    setSelectedContext(null)
  }

  const handleSetupVault = () => {
    navigate('/settings?tab=data')
  }

  // File drop handlers
  const handleDragOver = (e: React.DragEvent, contextId?: string) => {
    e.preventDefault()
    e.stopPropagation()
    if (process.env.NODE_ENV === 'development') {
      console.log('🔄 DRAG OVER:', contextId, 'Event target:', e.target)
    }
    setDragOver(contextId || 'general')
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (process.env.NODE_ENV === 'development') {
      console.log('🚪 DRAG LEAVE:', 'Event target:', e.target)
    }
    setDragOver(null)
  }

  const handleFileDrop = async (e: React.DragEvent, contextId?: string) => {
    e.preventDefault()
    e.stopPropagation()
    setDragOver(null)

    const files = Array.from(e.dataTransfer.files)
    if (files.length === 0) return

    // Find the target context
    const targetContext = contextId
      ? contexts.find(c => c.id === contextId)
      : null

    if (!targetContext) {
      console.warn('No target context found for file drop')
      return
    }

    // Use the new file processing logic with size check
    await processFilesForContext(files, targetContext)
  }

  const getColorClasses = (color: string) => {
    // Handle hex colors from vault data
    if (color.startsWith('#')) {
      return {
        gradient: 'from-primary/20 to-supplement2/20',
        border: 'border-primary/30 hover:border-primary/50',
        text: 'text-primary',
        bg: 'bg-primary',
        button: 'bg-primary text-gray-900 hover:bg-primary/80',
        dot: 'bg-primary'
      }
    }

    switch (color) {
      case 'primary':
        return {
          gradient: 'from-primary/20 to-supplement2/20',
          border: 'border-primary/30 hover:border-primary/50',
          text: 'text-primary',
          bg: 'bg-primary',
          button: 'bg-primary text-gray-900 hover:bg-primary/80',
          dot: 'bg-primary'
        }
      case 'secondary':
        return {
          gradient: 'from-secondary/20 to-supplement1/20',
          border: 'border-secondary/30 hover:border-secondary/50',
          text: 'text-secondary',
          bg: 'bg-secondary',
          button: 'bg-secondary text-white hover:bg-secondary/80',
          dot: 'bg-secondary'
        }
      case 'supplement2':
        return {
          gradient: 'from-supplement2/20 to-tertiary/20',
          border: 'border-supplement2/30 hover:border-supplement2/50',
          text: 'text-supplement2',
          bg: 'bg-supplement2',
          button: 'bg-supplement2 text-gray-900 hover:bg-supplement2/80',
          dot: 'bg-supplement2'
        }
      case 'supplement1':
        return {
          gradient: 'from-supplement1/20 to-primary/20',
          border: 'border-supplement1/30 hover:border-supplement1/50',
          text: 'text-supplement1',
          bg: 'bg-supplement1',
          button: 'bg-supplement1 text-gray-900 hover:bg-supplement1/80',
          dot: 'bg-supplement1'
        }
      default:
        return {
          gradient: 'from-primary/20 to-supplement2/20',
          border: 'border-primary/30 hover:border-primary/50',
          text: 'text-primary',
          bg: 'bg-primary',
          button: 'bg-primary text-gray-900 hover:bg-primary/80',
          dot: 'bg-primary'
        }
    }
  }

  return (
    <div className="flex-1 flex flex-col bg-gray-900">
      {/* Search and Controls Header */}
      <div className="p-6 border-b border-tertiary/50">
        {/* Hero Section */}
        <div className="mb-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Start New Chat */}
            <div className="bg-gradient-to-br from-primary/20 to-supplement2/20 border border-primary/30 rounded-xl p-6 hover:border-primary/50 transition-all">
              <div className="flex items-center gap-3 mb-3">
                <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                  <FontAwesomeIcon icon={ICONS.comment} className="text-gray-900" />
                </div>
                <h3 className="text-lg font-semibold text-primary">Start a New Chat</h3>
              </div>
              <p className="text-sm text-supplement1 mb-4 leading-relaxed">
                Something new to work on? Let's start from chatting with 300+ AI models first. Co-create your new content and then enjoy the smart context vault experience.
              </p>
              <button 
                onClick={handleNewChat}
                className="flex items-center gap-2 px-4 py-2 bg-primary text-gray-900 rounded-lg font-medium hover:bg-primary/80 transition-colors"
              >
                <FontAwesomeIcon icon={ICONS.comment} className="text-sm" />
                <span>New Chat</span>
              </button>
            </div>

            {/* Continue Thoughts */}
            <div className="bg-gradient-to-br from-secondary/20 to-supplement1/20 border border-secondary/30 rounded-xl p-6 hover:border-secondary/50 transition-all">
              <div className="flex items-center gap-3 mb-3">
                <div className="w-10 h-10 bg-secondary rounded-lg flex items-center justify-center">
                  <FontAwesomeIcon icon={ICONS.refresh} className="text-white" />
                </div>
                <h3 className="text-lg font-semibold text-secondary">Continue your Thoughts</h3>
              </div>
              <p className="text-sm text-supplement1 mb-4 leading-relaxed">
                Find or select the recent context below to continue your masterpiece!
              </p>
              <div className="h-10"></div> {/* Spacer to align with other cards */}
            </div>

            {/* Organize Content */}
            <div className="bg-gradient-to-br from-supplement2/20 to-tertiary/20 border border-supplement2/30 rounded-xl p-6 hover:border-supplement2/50 transition-all">
              <div className="flex items-center gap-3 mb-3">
                <div className="w-10 h-10 bg-supplement2 rounded-lg flex items-center justify-center">
                  <FontAwesomeIcon icon={ICONS.layerGroup} className="text-gray-900" />
                </div>
                <h3 className="text-lg font-semibold text-supplement2">Organize the Content</h3>
              </div>
              <p className="text-sm text-supplement1 mb-4 leading-relaxed">
                Let your local AI model to organize the thoughts in the file vaults
              </p>

              {/* Progress Display */}
              {isOrganizing && organizingProgress && (
                <div className="mb-3 p-2 bg-gray-900/50 rounded-lg border border-supplement2/30">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-xs text-gray-300">
                      {organizingProgress.current_file ? `Processing: ${organizingProgress.current_file}` : 'Organizing...'}
                    </span>
                    <span className="text-xs text-supplement2">
                      {organizingProgress.processed_files}/{organizingProgress.total_files}
                    </span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-1">
                    <div
                      className="bg-supplement2 h-1 rounded-full transition-all duration-300"
                      style={{ width: `${organizingProgress.overall_progress}%` }}
                    />
                  </div>
                </div>
              )}

              <div className="flex gap-2">
                <button
                  onClick={handleOrganize}
                  disabled={isOrganizing}
                  className="flex items-center gap-2 px-4 py-2 bg-supplement2 text-gray-900 rounded-lg font-medium hover:bg-supplement2/80 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <FontAwesomeIcon
                    icon={isOrganizing ? ICONS.arrowsRotate : ICONS.arrowsRotate}
                    className={`text-sm ${isOrganizing ? 'animate-spin' : ''}`}
                  />
                  <span>{isOrganizing ? 'Organizing...' : 'Organize'}</span>
                </button>

                <button
                  onClick={handleClearState}
                  disabled={isOrganizing}
                  className="flex items-center gap-2 px-3 py-2 bg-red-600 text-white rounded-lg font-medium hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Clear corrupted intelligence files"
                >
                  <FontAwesomeIcon icon={ICONS.trash} className="text-sm" />
                  <span>Clear</span>
                </button>

                <button
                  onClick={() => setShowPerformanceMonitor(true)}
                  className="flex items-center gap-2 px-3 py-2 bg-indigo-600 text-white rounded-lg font-medium hover:bg-indigo-700 transition-colors"
                  title="View file processing performance"
                >
                  <FontAwesomeIcon icon={ICONS.chartLine} className="text-sm" />
                  <span>Monitor</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-4">
          {/* View All Files Button */}
          {contexts.length > 0 && (
            <button
              onClick={handleViewAllFiles}
              className="flex items-center gap-2 px-4 py-2 bg-primary text-gray-900 rounded-lg font-medium hover:bg-primary/80 transition-colors"
            >
              <FontAwesomeIcon icon={ICONS.folder} className="text-sm" />
              <span>View All Files</span>
            </button>
          )}

          {/* Search Bar */}
          <div className="flex-1 relative">
            <FontAwesomeIcon
              icon={ICONS.search}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"
            />
            <input
              type="text"
              placeholder="Search contexts..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full bg-gray-800 border border-tertiary/50 rounded-lg pl-12 pr-4 py-3 text-supplement1 placeholder-gray-400 focus:outline-none focus:border-primary/50"
            />
          </div>

          {/* View Toggle */}
          <div className="flex items-center bg-gray-800 rounded-lg p-1 border border-tertiary/50">
            <button 
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-md ${viewMode === 'grid' ? 'bg-primary/20 text-primary' : 'text-gray-400 hover:text-supplement1'}`}
            >
              <FontAwesomeIcon icon={ICONS.grip} className="text-sm" />
            </button>
            <button 
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-md ${viewMode === 'list' ? 'bg-primary/20 text-primary' : 'text-gray-400 hover:text-supplement1'}`}
            >
              <FontAwesomeIcon icon={ICONS.list} className="text-sm" />
            </button>
          </div>

          {/* Sort Toggle */}
          <button className="flex items-center gap-2 px-4 py-3 bg-gray-800 border border-tertiary/50 rounded-lg text-supplement1 hover:bg-gray-700 transition-colors">
            <FontAwesomeIcon icon={ICONS.sort} className="text-sm" />
            <span className="text-sm">{sortBy}</span>
          </button>

          {/* TEMP: Streaming Demo Button (development only) */}
          {process.env.NODE_ENV === 'development' && (
            <button
              onClick={() => {
                alert(`🌊 STREAMING FILE UPLOAD READY!\n\n✅ VaultFileHandler service created\n✅ Streaming for files >10MB\n✅ Batch for files ≤10MB\n✅ Real-time progress tracking\n✅ HomePage integration working\n\n🧪 TEST NOW:\nDrop files on context cards below to see streaming in action!\n\nCheck console for detailed progress logs.`)
              }}
              className="px-3 py-3 bg-secondary border border-secondary/50 rounded-lg text-white hover:bg-secondary/80 transition-colors"
              title="Test streaming file uploads"
            >
              🌊
            </button>
          )}
        </div>
      </div>

      {/* Context Cards Grid */}
      <div className="flex-1 p-6 overflow-y-auto">
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <FontAwesomeIcon icon={ICONS.refresh} className="text-primary text-2xl mb-4 animate-spin" />
              <p className="text-supplement1">Loading your context vaults...</p>
            </div>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <p className="text-secondary mb-4">Error loading vaults: {error}</p>
              <button
                onClick={loadVaultData}
                className="px-4 py-2 bg-primary text-gray-900 rounded-lg hover:bg-primary/80 transition-colors"
              >
                Retry
              </button>
            </div>
          </div>
        ) : contexts.length === 0 ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center max-w-md">
              <FontAwesomeIcon icon={ICONS.folder} className="text-supplement1 text-4xl mb-4" />
              <h3 className="text-xl font-semibold text-supplement1 mb-2">Welcome to ChatLo!</h3>
              <p className="text-gray-400 mb-6">Set up your first context vault to get started organizing your files and conversations.</p>
              <button
                onClick={handleSetupVault}
                className="px-6 py-3 bg-primary text-gray-900 rounded-lg font-medium hover:bg-primary/80 transition-colors"
              >
                Set Up Vault
              </button>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {contexts.map((context) => {
              const colors = getColorClasses(context.color)
              const isDraggedOver = dragOver === context.id

              return (
                <div
                  key={context.id}
                  className={`context-card p-4 rounded-lg border transition-all hover:bg-gray-800/50 bg-gradient-to-br hover:transform hover:-translate-y-1 ${
                    isDraggedOver
                      ? 'border-primary/50 bg-primary/10'
                      : colors.border + ' ' + colors.gradient
                  }`}
                  onClick={() => handleContextSelect(context)}
                >
                  {/* REMOVED: Redundant drop zone overlay that was causing flickering */}

                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium text-sm truncate text-supplement1">{context.name}</h4>
                      <p className={`text-xs ${colors.text}/80`}>{context.contextType}</p>

                      {/* DEBUG: UUID and Path (hidden in production) */}
                      {process.env.NODE_ENV === 'development' && (
                        <div className="mt-2 p-2 bg-gray-900/50 rounded border border-gray-600 text-xs">
                          <div className="text-yellow-400 font-mono mb-1">
                            <span className="text-gray-400">UUID:</span> {context.id}
                          </div>
                          <div className="text-green-400 font-mono break-all">
                            <span className="text-gray-400">Path:</span> {context.path || 'Path not available'}
                          </div>
                        </div>
                      )}
                    </div>
                    <div className="flex items-center gap-1 ml-2">
                      <FontAwesomeIcon icon={ICONS.folder} className={`${colors.text} text-sm`} />
                    </div>
                  </div>

                  {/* File Drop Zone */}
                  <div
                    className={`mb-3 p-3 border-2 border-dashed rounded-lg text-center transition-colors ${
                      isDraggedOver
                        ? 'border-primary bg-primary/10'
                        : 'border-gray-600 hover:border-primary/50 hover:bg-gray-800/30'
                    }`}
                    onDragOver={(e) => {
                      e.preventDefault()
                      e.stopPropagation()
                      setDragOver(context.id)
                    }}
                    onDragLeave={(e) => {
                      e.preventDefault()
                      e.stopPropagation()
                      setDragOver(null)
                    }}
                    onDrop={(e) => {
                      e.preventDefault()
                      e.stopPropagation()
                      handleFileDrop(e, context.id)
                    }}
                  >
                    <FontAwesomeIcon
                      icon={ICONS.upload}
                      className={`text-lg mb-1 pointer-events-none ${isDraggedOver ? 'text-primary' : 'text-gray-400'}`}
                    />
                    <p className={`text-xs pointer-events-none ${isDraggedOver ? 'text-primary font-medium' : 'text-gray-400'}`}>
                      {isDraggedOver ? 'Release to drop files' : 'Drop files here'}
                    </p>
                  </div>

                  {/* Upload Button */}
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      handleDropZoneClick(context.id)
                    }}
                    className="w-full mb-3 px-3 py-2 bg-gray-700 text-supplement1 rounded text-xs hover:bg-gray-600 transition-colors flex items-center justify-center gap-2"
                  >
                    <FontAwesomeIcon icon={ICONS.upload} className="text-xs" />
                    <span>Upload Files</span>
                  </button>

                  <div className="flex items-center justify-between text-xs text-gray-500 mb-2">
                    <span className={colors.text}>{context.conversationCount} chats</span>
                    <span className={colors.text}>{context.fileCount} files</span>
                  </div>

                  <p className="text-xs text-gray-400 line-clamp-2 mb-3">{context.description}</p>

                  {/* Suggestions */}
                  {context.suggestedActions.length > 0 && (
                    <div className="mb-3">
                      <p className="text-xs text-gray-500 mb-1">💡 Suggestions:</p>
                      <ul className="text-xs text-gray-400 space-y-1">
                        {context.suggestedActions.slice(0, 2).map((action, index) => (
                          <li key={index}>• {action}</li>
                        ))}
                      </ul>
                    </div>
                  )}

                  <div className="flex items-center justify-between mt-3">
                    <div className="flex items-center gap-1">
                      <div className={`w-2 h-2 ${colors.dot} rounded-full`}></div>
                      <span className="text-xs text-gray-400">{context.lastActivity}</span>
                    </div>
                    <div className="flex gap-1">
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleContextChat(context.id)
                        }}
                        className="px-2 py-1 bg-gray-700 text-supplement1 rounded text-xs hover:bg-gray-600 transition-colors"
                      >
                        💬 Chat
                      </button>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        )}
      </div>

      {/* Enhanced Context Details Modal */}
      {selectedContext && (
        <VaultContextOverview
          context={selectedContext}
          onClose={closeContextDetails}
          onNavigateToChat={handleContextChat}
          onNavigateToFiles={handleViewAllFiles}
        />
      )}

      {/* Performance Monitor */}
      {showPerformanceMonitor && (
        <PerformanceMonitorPage
          onClose={() => setShowPerformanceMonitor(false)}
        />
      )}

    </div>
  )
}

export default HomePage
