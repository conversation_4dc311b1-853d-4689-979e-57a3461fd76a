# ChatLo Service Alignment Documentation

## 📋 Service Overview

This document provides a comprehensive alignment of all services in the ChatLo application, their roles, dependencies, and usage patterns.

---

## 🗂️ Core File Management Services

### **1. sharedDropboxService.ts**
**Role**: Context-aware file upload routing and shared dropbox management
**Status**: ✅ ACTIVE - Working fine with system IPC calls

**Primary Functions**:
- **Smart Upload Routing**: Determines upload destination based on context selection
- **Shared Dropbox Management**: Manages files when no context vault is selected
- **File Metadata Tracking**: Maintains `.files.json` for shared dropbox files
- **Service Reinitialization**: Handles vault root path changes

**Key Methods**:
- `getUploadDestination()` - Context-aware routing logic
- `uploadFile()` - File upload with destination routing
- `initialize()` - Service setup and folder creation

**Dependencies**:
- `contextVaultService` - For context selection state
- `window.electronAPI.vault.*` - File system operations
- `window.electronAPI.settings` - Vault root configuration

**Used By**:
- **InputArea.tsx**: "Browse" button file uploads to context vault
- **SettingsPage.tsx**: Service reinitialization after vault path changes
- **FilePicker.tsx**: Getting shared dropbox path for file scanning

---

### **2. vaultFileHandler.ts**
**Role**: Advanced file upload with streaming support and integrity verification
**Status**: ✅ ACTIVE - Modern streaming implementation

**Primary Functions**:
- **Streaming Upload**: Large file support with progress tracking
- **File Integrity Verification**: SHA-256 hash validation and corruption detection
- **Filename Preservation**: Minimal sanitization preserving original structure
- **Batch vs Streaming**: Automatic method selection based on file size

**Key Methods**:
- `uploadFile()` - Main upload with streaming/batch auto-selection
- `replaceSharedDropboxService()` - Compatibility layer for migration
- `verifyFileIntegrity()` - Post-upload integrity validation

**Dependencies**:
- `contextVaultService` - For context-aware uploads
- `window.electronAPI.vault.*` - File system operations
- `window.electronAPI.files.*` - File indexing

**Used By**:
- **HomePage.tsx**: File drop functionality
- **StreamingFileUploadDemo.tsx**: Streaming upload testing
- **InputArea.tsx**: Streaming upload path (alternative to sharedDropboxService)

---

### **3. contextVaultService.ts**
**Role**: Context vault management and selection state
**Status**: ✅ ACTIVE - Core vault management

**Primary Functions**:
- **Context Vault Discovery**: Scans and loads available context vaults
- **Selection State Management**: Tracks currently selected context
- **Context Creation**: Creates new context vaults with folder structure
- **Vault Registry Management**: Maintains vault metadata and paths

**Key Methods**:
- `refreshVaults()` - Reload vault registry
- `setSelectedContext()` - Context selection management
- `createContext()` - New context vault creation
- `getSelectedContext()` - Current context state

**Dependencies**:
- `window.electronAPI.vault.*` - Vault operations
- `window.electronAPI.settings` - Vault root configuration
- Local storage - Context selection persistence

**Used By**:
- **sharedDropboxService** - Upload destination routing
- **vaultFileHandler** - Context-aware uploads
- **CompactVaultSelector** - UI context selection
- **SettingsPage.tsx** - Vault management operations

---

## 🚀 Model and AI Services

### **4. localModelService.ts**
**Role**: Local AI model management (Ollama, LM Studio)
**Status**: ✅ ACTIVE - Local model integration

**Primary Functions**:
- **Ollama Integration**: Connect to local Ollama instance
- **LM Studio Integration**: Connect to local LM Studio
- **Model Discovery**: List available local models
- **Connection Health**: Monitor local service availability

**Dependencies**:
- `window.electronAPI.localModels.*` - Local model IPC calls
- HTTP fetch - Direct API calls to local services

**Used By**:
- **Model selection components** - Local model options
- **Chat interface** - Local model inference

---

### **5. openrouter.ts**
**Role**: OpenRouter API integration for cloud models
**Status**: ✅ ACTIVE - Cloud model access

**Primary Functions**:
- **API Key Management**: OpenRouter authentication
- **Model Catalog**: Available cloud models
- **Request Routing**: Cloud model inference

**Dependencies**:
- OpenRouter API - External service
- `window.electronAPI.settings` - API key storage

---

### **6. modelUpdateService.ts**
**Role**: Dynamic model updates and OTA model management
**Status**: ✅ ACTIVE - Model catalog updates

**Primary Functions**:
- **Model Manifest Updates**: JSON-based model catalog updates
- **OTA Model Updates**: Over-the-air model list updates
- **Version Management**: Model catalog versioning

**Dependencies**:
- External JSON endpoints - Model manifest sources
- Local storage - Model cache

---

## 🔧 Infrastructure Services

### **7. vaultInitializer.ts**
**Role**: Vault system initialization and setup
**Status**: ✅ ACTIVE - System setup

**Primary Functions**:
- **Initial Vault Setup**: First-time vault configuration
- **Folder Structure Creation**: Template-based folder creation
- **Welcome File Generation**: Default content creation

**Dependencies**:
- `window.electronAPI.vault.*` - File system operations
- Template files - Folder structure definitions

---

### **8. vaultUIManager.ts**
**Role**: Vault UI state management and coordination
**Status**: ✅ ACTIVE - UI coordination

**Primary Functions**:
- **UI State Coordination**: Vault-related UI state
- **Component Communication**: Inter-component messaging
- **UI Updates**: Reactive UI updates for vault changes

---

### **9. performanceMonitor.ts**
**Role**: Application performance monitoring
**Status**: ✅ ACTIVE - Performance tracking

**Primary Functions**:
- **Performance Metrics**: App performance monitoring
- **Resource Usage**: Memory and CPU tracking
- **Performance Logging**: Performance data collection

---

### **10. intelligenceService.ts**
**Role**: AI intelligence processing and analysis
**Status**: ✅ ACTIVE - AI processing

**Primary Functions**:
- **Content Analysis**: AI-powered content processing
- **Intelligence Extraction**: Data intelligence operations
- **AI Workflow Management**: Complex AI task coordination

---

## 📊 Service Dependency Map

```
┌─────────────────────────────────────────────────────────────┐
│                    Electron Main Process                    │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   File System   │  │   Vault API     │  │  Settings   │ │
│  │     (IPC)       │  │     (IPC)       │  │    (IPC)    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   Renderer Process Services                 │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              Core File Management Layer                 │ │
│  │  ┌─────────────────┐  ┌─────────────────┐              │ │
│  │  │contextVaultSvc  │  │sharedDropboxSvc │              │ │
│  │  │(State Manager)  │  │(Upload Router)  │              │ │
│  │  └─────────────────┘  └─────────────────┘              │ │
│  │           │                     │                      │ │
│  │           └─────────┬───────────┘                      │ │
│  │                     ▼                                  │ │
│  │           ┌─────────────────┐                          │ │
│  │           │vaultFileHandler │                          │ │
│  │           │(Streaming Impl) │                          │ │
│  │           └─────────────────┘                          │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                  AI/Model Layer                         │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────────┐   │ │
│  │  │localModelSvc│ │openrouterSvc│ │intelligenceSvc  │   │ │
│  │  └─────────────┘ └─────────────┘ └─────────────────┘   │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │               Infrastructure Layer                      │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────────┐   │ │
│  │  │vaultInit    │ │vaultUIManager│ │performanceMonitor│  │ │
│  │  └─────────────┘ └─────────────┘ └─────────────────┘   │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

---

## 🎯 Service Usage Patterns

### **File Upload Workflows**:

1. **Context Vault Upload** (InputArea "Browse"):
   ```
   User clicks Browse → sharedDropboxService.getUploadDestination() 
   → Returns context path → File uploaded to context/documents/
   ```

2. **Shared Dropbox Upload** (No context selected):
   ```
   User uploads file → sharedDropboxService.getUploadDestination() 
   → Returns shared-dropbox path → File tracked in .files.json
   ```

3. **Streaming Upload** (Large files):
   ```
   User drops large file → vaultFileHandler.uploadFile() 
   → Streaming with progress → Integrity verification
   ```

### **Vault Management Workflows**:

1. **Vault Path Change** (Settings):
   ```
   User selects new vault → contextVaultService.refreshVaults() 
   → sharedDropboxService.initialize() → Services reinitialized
   ```

2. **Context Creation** (Settings):
   ```
   User creates context → contextVaultService.createContext() 
   → Folder structure created → vaultInitializer templates applied
   ```

### **File Discovery Workflows**:

1. **File Picker Scanning** (FilePicker):
   ```
   FilePicker opens → sharedDropboxService.getUploadDestination() 
   → Scan shared-dropbox path → Display available files
   ```

---

## 📝 Key Insights

### **Service Complementarity**:
- **sharedDropboxService**: Smart routing and shared file management
- **vaultFileHandler**: Advanced upload capabilities and integrity
- **contextVaultService**: State management and vault operations

### **IPC Integration**:
All services properly use `window.electronAPI.*` for system integration:
- `vault.*` - File operations
- `settings.*` - Configuration
- `files.*` - File system dialogs

### **No Conflicts**:
Services work together harmoniously:
- sharedDropboxService handles routing logic
- vaultFileHandler provides advanced upload capabilities
- contextVaultService manages vault state

**Conclusion**: All services are properly aligned and serve distinct, complementary roles in the ChatLo ecosystem. The sharedDropboxService should be kept as it provides essential context-aware routing functionality that other services depend on.

---

*Last Updated: 2025-07-30 - Service alignment analysis complete*
