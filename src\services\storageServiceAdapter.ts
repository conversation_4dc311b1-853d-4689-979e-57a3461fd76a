/**
 * StorageServiceAdapter - Migration adapter for old intelligenceStorageService calls
 * 
 * This adapter provides the same interface as the old service but uses the new
 * RelativeStorageService underneath. This allows for gradual migration.
 */

import { relativeStorageService, storageContextFactory, IntelligenceData, StorageContext } from './relativeStorageService'
import { BaseService } from './base'
import { FileIntelligence } from '../types/fileIntelligenceTypes'

export interface LegacyStorageOptions {
  vaultPath?: string
  createDirectories?: boolean
}

export class StorageServiceAdapter extends BaseService {
  constructor() {
    super('StorageServiceAdapter')
  }

  /**
   * Initialize the service
   */
  protected async doInitialize(): Promise<void> {
    console.log('[STORAGE-ADAPTER] 🚀 Service initialized')
    // No specific initialization needed for this adapter
  }

  /**
   * Store file intelligence (legacy interface)
   * Converts old hardcoded path calls to new relative storage
   */
  async storeFileIntelligence(
    filePath: string,
    intelligence: IntelligenceData | FileIntelligence,
    options: LegacyStorageOptions = {}
  ): Promise<boolean> {
    return this.executeOperation(
      'storeFileIntelligence',
      async () => {
        console.log('[STORAGE-ADAPTER] 🔄 Converting legacy call to new storage system')
        console.log('[STORAGE-ADAPTER] 📁 Original file path:', filePath)
        
        try {
          // Convert legacy file path to storage context
          const context = this.createContextFromLegacyPath(filePath, options.vaultPath)

          console.log('[STORAGE-ADAPTER] ✅ Converted to context:', context)

          // Convert intelligence to standard format
          const standardIntelligence = this.convertToStandardIntelligence(filePath, intelligence)

          // Use new storage service
          const result = await relativeStorageService.storeIntelligence(context, standardIntelligence)
          
          if (!result.success) {
            console.error('[STORAGE-ADAPTER] ❌ Storage failed:', result.error)
            return false
          }
          
          console.log('[STORAGE-ADAPTER] ✅ Successfully stored using new architecture')
          return true
          
        } catch (error) {
          console.error('[STORAGE-ADAPTER] 💥 Error in legacy conversion:', error)
          this.logger.error('Failed to store intelligence via adapter', 'storeFileIntelligence', error as Error)
          return false
        }
      }
    )
  }

  /**
   * Get file intelligence (legacy interface)
   */
  async getFileIntelligence(
    filePath: string, 
    options: LegacyStorageOptions = {}
  ): Promise<IntelligenceData | null> {
    return this.executeOperation(
      'getFileIntelligence',
      async () => {
        console.log('[STORAGE-ADAPTER] 🔍 Converting legacy retrieval call')
        console.log('[STORAGE-ADAPTER] 📁 Original file path:', filePath)
        
        try {
          // Convert legacy file path to storage context
          const context = this.createContextFromLegacyPath(filePath, options.vaultPath)
          
          // Use new storage service
          const result = await relativeStorageService.getIntelligence(context)
          
          if (!result.success) {
            console.log('[STORAGE-ADAPTER] ℹ️ No intelligence found for path:', filePath)
            return null
          }
          
          console.log('[STORAGE-ADAPTER] ✅ Successfully retrieved using new architecture')
          return result.data || null
          
        } catch (error) {
          console.error('[STORAGE-ADAPTER] 💥 Error in legacy retrieval:', error)
          this.logger.error('Failed to retrieve intelligence via adapter', 'getFileIntelligence', error as Error)
          return null
        }
      }
    )
  }

  /**
   * Check if intelligence exists (legacy interface)
   */
  async hasFileIntelligence(
    filePath: string, 
    options: LegacyStorageOptions = {}
  ): Promise<boolean> {
    return this.executeOperation(
      'hasFileIntelligence',
      async () => {
        try {
          const context = this.createContextFromLegacyPath(filePath, options.vaultPath)
          const result = await relativeStorageService.hasIntelligence(context)
          return result.success && result.data || false
        } catch (error) {
          console.error('[STORAGE-ADAPTER] 💥 Error checking intelligence existence:', error)
          return false
        }
      }
    )
  }

  /**
   * Delete intelligence (legacy interface)
   */
  async deleteFileIntelligence(
    filePath: string,
    options: LegacyStorageOptions = {}
  ): Promise<boolean> {
    return this.executeOperation(
      'deleteFileIntelligence',
      async () => {
        try {
          const context = this.createContextFromLegacyPath(filePath, options.vaultPath)
          const result = await relativeStorageService.deleteIntelligence(context)
          return result.success && result.data || false
        } catch (error) {
          console.error('[STORAGE-ADAPTER] 💥 Error deleting intelligence:', error)
          return false
        }
      }
    )
  }

  /**
   * Clear vault intelligence state (legacy interface)
   * Converts old hardcoded vault path to new relative vault clearing
   */
  async clearVaultIntelligenceState(vaultPath: string): Promise<void> {
    return this.executeOperation(
      'clearVaultIntelligenceState',
      async () => {
        console.log('[STORAGE-ADAPTER] 🗑️ Converting legacy vault clearing call')
        console.log('[STORAGE-ADAPTER] 📁 Original vault path:', vaultPath)

        try {
          // Extract vault and context information from legacy path
          const { vaultId, contextId } = this.extractVaultInfoFromLegacyPath(vaultPath)

          console.log('[STORAGE-ADAPTER] ✅ Extracted vault info:', { vaultId, contextId })

          // Use new storage service for clearing
          const result = await relativeStorageService.clearVaultIntelligence(vaultId, contextId)

          if (!result.success) {
            throw new Error(`Failed to clear vault intelligence: ${result.error}`)
          }

          console.log('[STORAGE-ADAPTER] ✅ Successfully cleared vault using new architecture')

        } catch (error) {
          console.error('[STORAGE-ADAPTER] 💥 Error in legacy vault clearing:', error)
          this.logger.error('Failed to clear vault intelligence via adapter', 'clearVaultIntelligenceState', error as Error)
          throw error
        }
      }
    )
  }

  // ===== PRIVATE HELPERS =====

  /**
   * Convert legacy hardcoded file path to new storage context
   */
  private createContextFromLegacyPath(filePath: string, vaultPath?: string): StorageContext {
    console.log('[STORAGE-ADAPTER] 🔄 Converting legacy path to context')
    console.log('[STORAGE-ADAPTER] 📁 Input file path:', filePath)
    console.log('[STORAGE-ADAPTER] 📁 Input vault path:', vaultPath)
    
    // Remove hardcoded user path prefix if present
    let cleanPath = filePath
    const hardcodedPrefix = 'C:\\Users\\<USER>\\Documents\\Test20\\'
    if (cleanPath.startsWith(hardcodedPrefix)) {
      cleanPath = cleanPath.substring(hardcodedPrefix.length)
      console.log('[STORAGE-ADAPTER] 🧹 Removed hardcoded prefix, clean path:', cleanPath)
    }
    
    // Normalize path separators
    cleanPath = cleanPath.replace(/\\/g, '/')
    
    // Split path into components
    const pathParts = cleanPath.split('/')
    console.log('[STORAGE-ADAPTER] 📂 Path parts:', pathParts)
    
    // Extract vault and context
    let vaultId = 'default-vault'
    let contextId = 'default-context'
    let relativePath = cleanPath
    
    // Look for vault directory (contains 'vault' in name)
    const vaultIndex = pathParts.findIndex(part => part.includes('vault'))
    if (vaultIndex >= 0) {
      vaultId = pathParts[vaultIndex]
      
      // Context is usually the next directory after vault
      if (vaultIndex + 1 < pathParts.length) {
        contextId = pathParts[vaultIndex + 1]
        
        // Relative path starts after vault/context
        if (vaultIndex + 2 < pathParts.length) {
          relativePath = pathParts.slice(vaultIndex + 2).join('/')
        } else {
          relativePath = ''
        }
      }
    }
    
    const context: StorageContext = {
      vaultId,
      contextId,
      relativePath
    }
    
    console.log('[STORAGE-ADAPTER] ✅ Created context:', context)
    console.log('[STORAGE-ADAPTER] 🎯 No hardcoded paths in result ✅')
    
    return context
  }

  /**
   * Extract vault and context information from legacy vault path
   */
  private extractVaultInfoFromLegacyPath(vaultPath: string): { vaultId: string, contextId?: string } {
    console.log('[STORAGE-ADAPTER] 🔍 Extracting vault info from legacy path:', vaultPath)

    // Remove hardcoded user path prefix if present
    let cleanPath = vaultPath
    const hardcodedPrefix = 'C:\\Users\\<USER>\\Documents\\Test20\\'
    if (cleanPath.startsWith(hardcodedPrefix)) {
      cleanPath = cleanPath.substring(hardcodedPrefix.length)
      console.log('[STORAGE-ADAPTER] 🧹 Removed hardcoded prefix, clean path:', cleanPath)
    }

    // Normalize path separators
    cleanPath = cleanPath.replace(/\\/g, '/')

    // Split path into components
    const pathParts = cleanPath.split('/')
    console.log('[STORAGE-ADAPTER] 📂 Path parts:', pathParts)

    // Extract vault and context
    let vaultId = 'default-vault'
    let contextId: string | undefined

    // Look for vault directory (contains 'vault' in name)
    const vaultIndex = pathParts.findIndex(part => part.includes('vault'))
    if (vaultIndex >= 0) {
      vaultId = pathParts[vaultIndex]

      // Context is usually the next directory after vault
      if (vaultIndex + 1 < pathParts.length) {
        contextId = pathParts[vaultIndex + 1]
      }
    }

    const result = { vaultId, contextId }
    console.log('[STORAGE-ADAPTER] ✅ Extracted vault info:', result)

    return result
  }

  /**
   * Convert different intelligence formats to standard IntelligenceData format
   */
  private convertToStandardIntelligence(filePath: string, intelligence: IntelligenceData | FileIntelligence): IntelligenceData {
    // If already in standard format, return as-is
    if ('document_hash' in intelligence && 'analysis_timestamp' in intelligence) {
      return intelligence as IntelligenceData
    }

    // Convert FileIntelligence to IntelligenceData
    const fileIntel = intelligence as FileIntelligence

    console.log('[STORAGE-ADAPTER] 🔄 Converting FileIntelligence to IntelligenceData')
    console.log('[STORAGE-ADAPTER] 📊 Input key_ideas count:', fileIntel.key_ideas?.length || 0)
    console.log('[STORAGE-ADAPTER] 📊 Sample key_ideas:', fileIntel.key_ideas?.slice(0, 2).map(idea => ({
      text: idea.text,
      score: idea.relevance_score,
      intents: idea.intent_types
    })))

    const standardIntelligence: IntelligenceData = {
      document_hash: this.generateFileHash(filePath),
      file_path: fileIntel.file_path || filePath,
      analysis_timestamp: fileIntel.updated_at || new Date().toISOString(),
      labels: fileIntel.key_ideas?.map(idea => idea.text) || [],
      summary: fileIntel.summary || '',
      key_points: fileIntel.key_ideas?.map(idea => idea.text) || [],
      // PRESERVE RICH DATA: Store the full FileIntelligence object to avoid data loss
      key_ideas: fileIntel.key_ideas || [],
      weighted_entities: fileIntel.weighted_entities || [],
      human_connections: fileIntel.human_connections || [],
      processing_confidence: fileIntel.processing_confidence || 0,
      analysis_metadata: fileIntel.analysis_metadata || {},
      created_at: fileIntel.created_at || new Date().toISOString(),
      updated_at: fileIntel.updated_at || new Date().toISOString()
    }

    console.log('[STORAGE-ADAPTER] ✅ Converted FileIntelligence to IntelligenceData')
    console.log('[STORAGE-ADAPTER] 📊 Output labels count:', standardIntelligence.labels.length)
    console.log('[STORAGE-ADAPTER] 📊 Output key_ideas count:', standardIntelligence.key_ideas?.length || 0)

    return standardIntelligence
  }

  /**
   * Generate file hash for intelligence storage
   */
  private generateFileHash(filePath: string): string {
    // Simple hash generation - matches the new service
    let hash = 0
    for (let i = 0; i < filePath.length; i++) {
      const char = filePath.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash
    }
    return Math.abs(hash).toString(16).substring(0, 8)
  }
}

// Export singleton instance
export const storageServiceAdapter = new StorageServiceAdapter()

// Export types for compatibility
export type { IntelligenceData, StorageContext } from './relativeStorageService'
