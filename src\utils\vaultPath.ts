// Vault path utilities
// Robust, platform-safe extraction of a context path from a full file path

/**
 * Normalize to forward slashes for internal processing.
 */
function toPosix(path: string): string {
  return path.replace(/\\/g, '/');
}

/**
 * Restore original separator style found in the input path.
 */
function restoreSeparators(posixPath: string, originalPath: string): string {
  const usesBackslash = originalPath.includes('\\');
  return usesBackslash ? posixPath.replace(/\//g, '\\') : posixPath;
}

/**
 * Extract the context directory from a file path based on known vault structure.
 * Expected structure examples:
 * - <vault-root>/<context>/documents/<file>
 * - <vault-root>/<context>/images/<file>
 * - <vault-root>/<context>/artifacts/<file>
 * Fallback: parent directory of the file.
 */
export function extractContextPath(filePath: string): string {
  try {
    console.log('[LABELS] 🗂️ extractContextPath called with:', filePath)

    if (!filePath || typeof filePath !== 'string') {
      console.log('[LABELS] 🗂️ Invalid filePath, returning empty string')
      return ''
    }

    // Normalize to forward slashes for parsing
    const posix = toPosix(filePath)
    console.log('[LABELS] 🗂️ Normalized to posix:', posix)

    // Strategy 1: Detect common subdirectories within a context
    const subDirs = ['documents', 'images', 'artifacts']
    for (const subDir of subDirs) {
      const token = `/${subDir}/`
      const idx = posix.lastIndexOf(token)
      if (idx !== -1) {
        const contextPathPosix = posix.substring(0, idx)
        const result = restoreSeparators(contextPathPosix, filePath)
        console.log('[LABELS] 🗂️ ✅ Found context path using strategy 1 (subdir:', subDir, '):', result)
        return result
      }
    }

    // Strategy 2: If the file is directly under the context (rare), drop the filename
    const lastSlash = posix.lastIndexOf('/')
    if (lastSlash > 0) {
      const parentPosix = posix.substring(0, lastSlash)
      const result = restoreSeparators(parentPosix, filePath)
      console.log('[LABELS] 🗂️ ⚠️ Using strategy 2 (parent directory):', result)
      return result
    }

    // Strategy 3: Give up gracefully
    console.log('[LABELS] 🗂️ ❌ No context path found, returning empty string')
    return ''
  } catch (error) {
    console.log('[LABELS] 🗂️ ❌ Error in extractContextPath:', error)
    return ''
  }
}

/**
 * Join path segments using forward slashes, then restore to original style of a base path.
 */
export function joinLike(basePath: string, ...segments: string[]): string {
  const posix = [toPosix(basePath), ...segments.map(toPosix)].join('/').replace(/\/+/, '/');
  return restoreSeparators(posix, basePath);
}

// (Note) Previous duplicate definitions removed to satisfy Vite/esbuild
