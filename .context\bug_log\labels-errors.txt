vaultUIManager.ts:13 [LABELS] 🗂️ Using standardized joinLike from vaultPath.ts
vaultUIManager.ts:22 [LABELS] 🗂️ ✅ Path joined: C:\Users\<USER>\Documents\Test20\personal-vault\getting-started\.context\metadata.json
vaultUIManager.ts:13 [LABELS] 🗂️ Using standardized joinLike from vaultPath.ts
vaultUIManager.ts:22 [LABELS] 🗂️ ✅ Path joined: C:\Users\<USER>\Documents\Test20\personal-vault\getting-started\master.md
vaultUIManager.ts:13 [LABELS] 🗂️ Using standardized joinLike from vaultPath.ts
vaultUIManager.ts:22 [LABELS] 🗂️ ✅ Path joined: C:\Users\<USER>\Documents\Test20\work-vault\project\.context\metadata.json
vaultUIManager.ts:13 [LABELS] 🗂️ Using standardized joinLike from vaultPath.ts
vaultUIManager.ts:22 [LABELS] 🗂️ ✅ Path joined: C:\Users\<USER>\Documents\Test20\work-vault\project\master.md
vaultUIManager.ts:13 [LABELS] 🗂️ Using standardized joinLike from vaultPath.ts
vaultUIManager.ts:22 [LABELS] 🗂️ ✅ Path joined: C:\Users\<USER>\Documents\Test20\personal-vault\getting-started\.context\metadata.json
vaultUIManager.ts:13 [LABELS] 🗂️ Using standardized joinLike from vaultPath.ts
vaultUIManager.ts:22 [LABELS] 🗂️ ✅ Path joined: C:\Users\<USER>\Documents\Test20\personal-vault\getting-started\master.md
vaultUIManager.ts:13 [LABELS] 🗂️ Using standardized joinLike from vaultPath.ts
vaultUIManager.ts:22 [LABELS] 🗂️ ✅ Path joined: C:\Users\<USER>\Documents\Test20\work-vault\project\.context\metadata.json
vaultUIManager.ts:13 [LABELS] 🗂️ Using standardized joinLike from vaultPath.ts
vaultUIManager.ts:22 [LABELS] 🗂️ ✅ Path joined: C:\Users\<USER>\Documents\Test20\work-vault\project\master.md
fileAnalysisService.ts:960 [LABELS] 🤖 analyzeAndStoreDocument called
fileAnalysisService.ts:961 [LABELS] 🤖 filePath: C:\Users\<USER>\Documents\Test20\personal-vault/getting-started/documents/modelUpdate-externalization.md
fileAnalysisService.ts:962 [LABELS] 🤖 vaultPath: C:\Users\<USER>\Documents\Test20\personal-vault
fileAnalysisService.ts:963 [LABELS] 🤖 content length: 5246
fileAnalysisService.ts:970 [LABELS] 🤖 Starting document analysis...
fileAnalysisService.ts:71 [LABELS] 🤖 Attempting local model analysis...
fileAnalysisService.ts:136 [LABELS] 🔍 ===== SYSTEMATIC SERVICE CHECK =====
fileAnalysisService.ts:137 [LABELS] 🔍 1. Checking local model service health...
fileAnalysisService.ts:142 [LABELS] 🔍 Provider Status: {ollama: {…}, lmstudio: {…}}
fileAnalysisService.ts:166 [LABELS] 🔍 2. Checking model availability...
fileAnalysisService.ts:167 [LABELS] 🔍 Requested model: ollama:gemma3:latest
vaultUIManager.ts:13 [LABELS] 🗂️ Using standardized joinLike from vaultPath.ts
vaultUIManager.ts:22 [LABELS] 🗂️ ✅ Path joined: C:\Users\<USER>\Documents\Test20\personal-vault\getting-started\.context\metadata.json
vaultUIManager.ts:13 [LABELS] 🗂️ Using standardized joinLike from vaultPath.ts
vaultUIManager.ts:22 [LABELS] 🗂️ ✅ Path joined: C:\Users\<USER>\Documents\Test20\personal-vault\getting-started\master.md
fileAnalysisService.ts:170 [LABELS] 🔍 Available models: (5) [{…}, {…}, {…}, {…}, {…}]
fileAnalysisService.ts:192 [LABELS] 🔍 ✅ Model found: gemma3:latest
fileAnalysisService.ts:193 [LABELS] 🔍 ===== SERVICE CHECK COMPLETE =====
fileAnalysisService.ts:194 [LABELS] 🤖 Using model: gemma3:latest
fileAnalysisService.ts:200 [LABELS] 🤖 📡 Sending request to local model: ollama:gemma3:latest
fileAnalysisService.ts:201 [LABELS] 🤖 📡 Prompt preview: You are a precise extractor. Do NOT explain. Output exactly ONE fenced Markdown block, nothing else.

Task
- Read the document between DOC and DOC.
- Produce 10 key ideas as 1–4 word noun phrases (no verbs, no punctuation).
- Be tolerant: if the doc is short, output as many as you can (≥3).
- Use on...
vaultUIManager.ts:13 [LABELS] 🗂️ Using standardized joinLike from vaultPath.ts
vaultUIManager.ts:22 [LABELS] 🗂️ ✅ Path joined: C:\Users\<USER>\Documents\Test20\work-vault\project\.context\metadata.json
vaultUIManager.ts:13 [LABELS] 🗂️ Using standardized joinLike from vaultPath.ts
vaultUIManager.ts:22 [LABELS] 🗂️ ✅ Path joined: C:\Users\<USER>\Documents\Test20\work-vault\project\master.md
fileAnalysisService.ts:210 [LABELS] 🤖 📝 ===== RAW LLM RESPONSE ANALYSIS =====
fileAnalysisService.ts:211 [LABELS] 🤖 📝 Response type: string
fileAnalysisService.ts:212 [LABELS] 🤖 📝 Response length: 1362
fileAnalysisService.ts:215 [LABELS] 🤖 📝 FULL RESPONSE:
fileAnalysisService.ts:225 [LABELS] 🤖 📝 DEBUG: Raw response written to: C:\Users\<USER>\Documents\Test20\debug_llm_response.txt
fileAnalysisService.ts:237 [LABELS] 🤖 📝 Format analysis: {hasFileIntelBlock: true, hasJsonFence: true, hasJsonObject: false, hasKeyIdeas: false, isEmpty: false}
fileAnalysisService.ts:247 [LABELS] 🤖 📝 First 10 lines:
fileAnalysisService.ts:249 [LABELS] 🤖 📝 Line 1: "```markdown"
fileAnalysisService.ts:249 [LABELS] 🤖 📝 Line 2: "!-- FILE_INTEL:BEGIN --"
fileAnalysisService.ts:249 [LABELS] 🤖 📝 Line 3: "## Key Ideas (1–4 words each)"
fileAnalysisService.ts:249 [LABELS] 🤖 📝 Line 4: "- "Project Management" ; score=99; intents=topic,action; context=long-term strategy; entities=Agile,Scrum"
fileAnalysisService.ts:249 [LABELS] 🤖 📝 Line 5: "- "Risk Assessment" ; score=97; intents=knowledge,action; context=strategic planning; entities=Probability,Impact"
fileAnalysisService.ts:249 [LABELS] 🤖 📝 Line 6: "- "Stakeholder Engagement" ; score=95; intents=connection,action; context=communication channels; entities=Feedback,Meetings"
fileAnalysisService.ts:249 [LABELS] 🤖 📝 Line 7: "- "Resource Allocation" ; score=92; intents=knowledge,action; context=budget management; entities=Personnel,Equipment"
fileAnalysisService.ts:249 [LABELS] 🤖 📝 Line 8: "- "Performance Metrics" ; score=88; intents=topic,knowledge; context=KPIs,measurement; entities=ROI,Efficiency"
fileAnalysisService.ts:249 [LABELS] 🤖 📝 Line 9: "- "Change Management" ; score=85; intents=topic,action; context=transition process; entities=Resistance,Adoption"
fileAnalysisService.ts:249 [LABELS] 🤖 📝 Line 10: "- "Strategic Alignment" ; score=82; intents=connection,knowledge; context=business goals; entities=Vision,Mission"
fileAnalysisService.ts:254 [LABELS] 🤖 📝 ===== END RESPONSE ANALYSIS =====
fileAnalysisService.ts:329 [LABELS] 🤖 📝 Parsing AI model response...
fileAnalysisService.ts:330 [LABELS] 🤖 📝 Response length: 1362
fileAnalysisService.ts:331 [LABELS] 🤖 📝 Response preview: ```markdown
!-- FILE_INTEL:BEGIN --
## Key Ideas (1–4 words each)
- "Project Management" ; score=99; intents=topic,action; context=long-term strategy; entities=Agile,Scrum
- "Risk Assessment" ; score=97; intents=knowledge,action; context=strategic planning; entities=Probability,Impact
- "Stakeholder Engagement" ; score=95; intents=connection,action; context=communication channels; entities=Feedback,Meetings
- "Resource Allocation" ; score=92; intents=knowledge,action; context=budget management; 
fileAnalysisService.ts:334 [LABELS] 🤖 📝 Attempting to extract JSON payload...
fileAnalysisService.ts:402 [LABELS] 🔄 ===== MARKDOWN → JSON CONVERSION =====
fileAnalysisService.ts:403 [LABELS] 🔄 Starting FILE_INTEL block extraction...
fileAnalysisService.ts:408 [LABELS] 🔄 Looking for markers: {startMarker: '!-- FILE_INTEL:BEGIN --', endMarker: '!-- FILE_INTEL:END --'}
fileAnalysisService.ts:412 [LABELS] 🔄 Marker positions: {startIdx: 12, endIdx: -1}
fileAnalysisService.ts:528 [LABELS] 🔄 ❌ FILE_INTEL markers not found
fileAnalysisService.ts:529 [LABELS] 🔄 Response contains "!-- FILE_INTEL:BEGIN --": true
fileAnalysisService.ts:530 [LABELS] 🔄 Response contains "!-- FILE_INTEL:END --": false
fileAnalysisService.ts:541 [LABELS] 🤖 📝 Looking for JSON fences in response...
fileAnalysisService.ts:543 [LABELS] 🤖 📝 JSON fence match result: FOUND
fileAnalysisService.ts:554 [LABELS] 🤖 📝 ✅ JSON fence found, content length: 1355
fileAnalysisService.ts:556 [LABELS] 🤖 📝 Found JSON fence, attempting to parse...
fileAnalysisService.ts:558 [LABELS] 🤖 📝 Sanitized JSON: markdown
!-- FILE_INTEL:BEGIN --
## Key Ideas (1–4 words each)
- "Project Management" ; score=99; intents=topic,action; context=long-term strategy; entities=Agile,Scrum
- "Risk Assessment" ; score=97; intents=knowledge,action; context=strategic planning; entities=Probability,Impact
- "Stakeholder En...
fileAnalysisService.ts:605 [LABELS] 🤖 📝 ❌ Fenced JSON parsing failed: Unexpected token 'm', "markdown
!"... is not valid JSON
fileAnalysisService.ts:606 [LABELS] 🤖 📝 ❌ Error details: SyntaxError: Unexpected token 'm', "markdown
!"... is not valid JSON
    at JSON.parse (<anonymous>)
    at FileAnalysisService.extractJsonPayload (fileAnalysisService.ts:560:26)
    at FileAnalysisService.parseAndValidateKeyIdeas (fileAnalysisService.ts:335:27)
    at FileAnalysisService.extractKeyIdeasWithLocalModel (fileAnalysisService.ts:262:17)
    at async executeOperationOrThrow.contentLength (fileAnalysisService.ts:72:22)
    at async wrapServiceOperation (ServiceError.ts:191:20)
    at async BaseService.ts:214:22
    at async PerformanceLogger.measureAsync (ServiceLogger.ts:261:22)
    at async FileAnalysisService.executeOperation (BaseService.ts:213:12)
    at async FileAnalysisService.executeOperationOrThrow (BaseService.ts:234:20)
fileAnalysisService.ts:607 [LABELS] 🤖 📝 ❌ Raw fence content: markdown
!-- FILE_INTEL:BEGIN --
## Key Ideas (1–4 words each)
- "Project Management" ; score=99; intents=topic,action; context=long-term strategy; entities=Agile,Scrum
- "Risk Assessment" ; score=97; intents=knowledge,action; context=strategic planning; entities=Probability,Impact
- "Stakeholder Engagement" ; score=95; intents=connection,action; context=communication channels; entities=Feedback,Meetings
- "Resource Allocation" ; score=92; intents=knowledge,action; context=budget management; ent
fileAnalysisService.ts:620 [LABELS] 🤖 📝 ❌ Throwing "No file intel block or JSON found" error
fileAnalysisService.ts:389 [LABELS] 🤖 📝 ❌ Failed to parse AI response: No file intel block or JSON found in response
fileAnalysisService.ts:390 [LABELS] 🤖 📝 ❌ Error type: Error
fileAnalysisService.ts:75 [LABELS] 🤖 ❌ Local model analysis failed: No file intel block or JSON found in response
fileAnalysisService.ts:79 [LABELS] 🤖 🔄 Using keyword-based fallback...
fileAnalysisService.ts:83 [LABELS] 🤖 ✅ Keyword fallback successful, extracted 10 ideas
fileAnalysisService.ts:972 [LABELS] 🤖 ✅ Document analysis completed successfully
fileAnalysisService.ts:1012 [LABELS] 🤖 Storing intelligence data...
fileAnalysisService.ts:1013 [LABELS] 🤖 fileIntelligence.file_path: C:\Users\<USER>\Documents\Test20\personal-vault/getting-started/documents/modelUpdate-externalization.md
fileAnalysisService.ts:1014 [LABELS] 🤖 fileIntelligence.key_ideas.length: 10
relativeStorageService.ts:339 [RELATIVE-STORAGE] 💾 Intelligence data preview: {document_hash: '70f190a4', file_path: 'C:\\Users\\<USER>\\Documents\\Test20\\personal-vault/getting-started/documents/modelUpdate-externalization.md', analysis_timestamp: '2025-08-09T10:30:12.418Z', labels_count: 10, key_points_count: 10}
fileAnalysisService.ts:1021 [LABELS] 🤖 ✅ Intelligence data stored successfully
fileAnalysisService.ts:1024 [LABELS] 🤖 Updating vault intelligence...
fileAnalysisService.ts:1026 [LABELS] 🤖 ⚠️ Vault intelligence update temporarily disabled during migration
fileAnalysisService.ts:1027 [LABELS] 🤖 ✅ Vault intelligence updated
