/**
 * Intelligent Cache Manager Service
 * Implements multi-tier caching for file processing and intelligence data
 * Optimized for 32GB+ systems with smart memory management
 */

import { BaseService } from './base'
import { performanceMonitor } from './performanceMonitor'

interface CacheEntry<T> {
  data: T
  timestamp: number
  accessCount: number
  lastAccessed: number
  size: number
}

interface CacheStats {
  hotCacheSize: number
  warmCacheSize: number
  coldCacheSize: number
  totalEntries: number
  hitRate: number
  evictions: number
}

interface CacheConfig {
  hotCacheMaxSize: number    // Memory cache limit (MB)
  warmCacheMaxSize: number   // Disk cache limit (MB)
  coldCacheMaxSize: number   // Archive cache limit (MB)
  maxAge: number            // Max age before eviction (ms)
  compressionEnabled: boolean
}

class CacheManager extends BaseService {
  // Hot cache: In-memory LRU cache for frequently accessed data
  private hotCache = new Map<string, CacheEntry<any>>()

  // Browser-compatible cache storage
  private warmCacheStorage = new Map<string, any>() // Browser-compatible warm cache
  private coldCacheStorage = new Map<string, string>() // Browser-compatible cold cache (compressed)
  
  // Configuration
  private config: CacheConfig = {
    hotCacheMaxSize: 256 * 1024 * 1024,  // 256MB memory cache
    warmCacheMaxSize: 1024 * 1024 * 1024, // 1GB disk cache
    coldCacheMaxSize: 4 * 1024 * 1024 * 1024, // 4GB archive cache
    maxAge: 7 * 24 * 60 * 60 * 1000,     // 7 days
    compressionEnabled: true
  }
  
  // Statistics
  private stats = {
    hits: 0,
    misses: 0,
    evictions: 0,
    totalRequests: 0
  }

  constructor() {
    super({
      name: 'CacheManager',
      // IMPORTANT: Avoid auto-initialize in base constructor because subclass
      // fields (e.g., hotCache) are not yet initialized at that time.
      autoInitialize: false
    })
    // Now that subclass fields are initialized, safely initialize the service
    // without racing field initializers.
    void this.initialize()
  }

  protected async doInitialize(): Promise<void> {
    // Initialize browser-compatible cache configuration
    this.config = this.getAdaptiveCacheConfig()

    // Load existing cache from localStorage if available
    this.loadCacheFromStorage()

    // Start cleanup interval
    setInterval(() => this.performMaintenance(), 5 * 60 * 1000) // Every 5 minutes

    this.logger.info('Cache manager initialized', 'doInitialize', {
      config: this.config,
      hotCacheEntries: this.hotCache ? this.hotCache.size : 0
    })
  }

  /**
   * Get adaptive cache configuration based on system capabilities
   */
  private getAdaptiveCacheConfig(): CacheConfig {
    // Get system profile from performance monitor
    const systemProfile = performanceMonitor.getSystemProfile()

    if (systemProfile) {
      switch (systemProfile.performanceTier) {
        case 'minimum':
          return {
            hotCacheMaxSize: 128 * 1024 * 1024,    // 128MB memory cache
            warmCacheMaxSize: 512 * 1024 * 1024,   // 512MB browser cache
            coldCacheMaxSize: 1024 * 1024 * 1024,  // 1GB compressed cache
            maxAge: 3 * 24 * 60 * 60 * 1000,       // 3 days
            compressionEnabled: true
          }
        case 'mid-range':
          return {
            hotCacheMaxSize: 256 * 1024 * 1024,    // 256MB memory cache
            warmCacheMaxSize: 1024 * 1024 * 1024,  // 1GB browser cache
            coldCacheMaxSize: 2 * 1024 * 1024 * 1024, // 2GB compressed cache
            maxAge: 7 * 24 * 60 * 60 * 1000,       // 7 days
            compressionEnabled: true
          }
        case 'high-end':
          return {
            hotCacheMaxSize: 512 * 1024 * 1024,    // 512MB memory cache
            warmCacheMaxSize: 2 * 1024 * 1024 * 1024, // 2GB browser cache
            coldCacheMaxSize: 4 * 1024 * 1024 * 1024, // 4GB compressed cache
            maxAge: 14 * 24 * 60 * 60 * 1000,      // 14 days
            compressionEnabled: true
          }
        default:
          break
      }
    }

    // Default configuration
    return {
      hotCacheMaxSize: 256 * 1024 * 1024,    // 256MB memory cache
      warmCacheMaxSize: 1024 * 1024 * 1024,  // 1GB browser cache
      coldCacheMaxSize: 2 * 1024 * 1024 * 1024, // 2GB compressed cache
      maxAge: 7 * 24 * 60 * 60 * 1000,       // 7 days
      compressionEnabled: true
    }
  }

  protected async doHealthCheck(): Promise<boolean> {
    try {
      // Check if localStorage is available and cache is functional
      const testKey = 'cache_health_test'
      const testValue = { test: true, timestamp: Date.now() }

      localStorage.setItem(testKey, JSON.stringify(testValue))
      const retrieved = localStorage.getItem(testKey)
      localStorage.removeItem(testKey)

      // Check if we can process based on memory usage
      const canProcess = performanceMonitor.canProcess('quick')

      const isHealthy = retrieved !== null && canProcess
      this.logger.info('Cache health check completed', 'doHealthCheck', {
        localStorageAvailable: retrieved !== null,
        canProcess,
        isHealthy
      })

      return isHealthy
    } catch (error) {
      this.logger.warn('Cache health check failed', 'doHealthCheck', error)
      return false
    }
  }

  /**
   * Load cache from localStorage (browser-compatible)
   */
  private loadCacheFromStorage(): void {
    try {
      // Load warm cache from localStorage
      const warmCacheData = localStorage.getItem('chatlo_warm_cache')
      if (warmCacheData) {
        const parsed = JSON.parse(warmCacheData)
        this.warmCacheStorage = new Map(Object.entries(parsed))
      }

      // Load cold cache from localStorage
      const coldCacheData = localStorage.getItem('chatlo_cold_cache')
      if (coldCacheData) {
        const parsed = JSON.parse(coldCacheData)
        this.coldCacheStorage = new Map(Object.entries(parsed))
      }

      console.log(`🔥 [CACHE] Loaded cache from storage: ${this.warmCacheStorage.size} warm, ${this.coldCacheStorage.size} cold`)
    } catch (error) {
      console.warn('Failed to load cache from storage:', error)
    }
  }

  /**
   * Save cache to localStorage (browser-compatible)
   */
  private saveCacheToStorage(): void {
    try {
      // Save warm cache to localStorage
      const warmCacheObj = Object.fromEntries(this.warmCacheStorage)
      localStorage.setItem('chatlo_warm_cache', JSON.stringify(warmCacheObj))

      // Save cold cache to localStorage
      const coldCacheObj = Object.fromEntries(this.coldCacheStorage)
      localStorage.setItem('chatlo_cold_cache', JSON.stringify(coldCacheObj))
    } catch (error) {
      console.warn('Failed to save cache to storage:', error)
    }
  }

  /**
   * Get data from cache (checks all tiers)
   */
  async get<T>(key: string): Promise<T | null> {
    this.stats.totalRequests++
    
    try {
      // 1. Check hot cache (memory) - instant
      const hotEntry = this.hotCache.get(key)
      if (hotEntry) {
        hotEntry.accessCount++
        hotEntry.lastAccessed = Date.now()
        this.stats.hits++
        
        console.log(`🔥 [CACHE] Hot cache hit: ${key}`)
        return hotEntry.data as T
      }

      // 2. Check warm cache (browser storage) - fast
      if (this.warmCacheStorage.has(key)) {
        const data = this.warmCacheStorage.get(key) as T
        if (data) {
          // Promote to hot cache
          await this.setHotCache(key, data)
          this.stats.hits++

          console.log(`🌡️ [CACHE] Warm cache hit: ${key}`)
          return data
        }
      }

      // 3. Check cold cache (compressed browser storage) - slower
      if (this.coldCacheStorage.has(key)) {
        const compressedData = this.coldCacheStorage.get(key)
        if (compressedData) {
          try {
            // Simple decompression (base64 decode for browser compatibility)
            const jsonString = atob(compressedData)
            const data = JSON.parse(jsonString) as T

            // Promote to warm and hot cache
            this.warmCacheStorage.set(key, data)
            await this.setHotCache(key, data)
            this.stats.hits++

            console.log(`🧊 [CACHE] Cold cache hit: ${key}`)
            return data
          } catch (error) {
            console.warn(`Failed to decompress cold cache data for key: ${key}`, error)
          }
        }
      }

      // Cache miss
      this.stats.misses++
      console.log(`❌ [CACHE] Cache miss: ${key}`)
      return null
      
    } catch (error) {
      this.logger.error('Cache get failed', 'get', { key, error })
      this.stats.misses++
      return null
    }
  }

  /**
   * Set data in cache (stores in all appropriate tiers)
   */
  async set<T>(key: string, data: T, options?: { skipHot?: boolean, skipWarm?: boolean }): Promise<void> {
    try {
      const dataSize = this.estimateSize(data)
      
      // Store in hot cache (unless skipped or too large)
      if (!options?.skipHot && dataSize < this.config.hotCacheMaxSize / 10) {
        await this.setHotCache(key, data)
      }
      
      // Store in warm cache (unless skipped)
      if (!options?.skipWarm) {
        this.warmCacheStorage.set(key, data)
      }

      // Store in cold cache for long-term storage (compressed)
      try {
        const jsonString = JSON.stringify(data)
        const compressedData = btoa(jsonString) // Base64 encode for browser compatibility
        this.coldCacheStorage.set(key, compressedData)
      } catch (error) {
        console.warn(`Failed to compress data for cold cache: ${key}`, error)
      }

      // Persist to localStorage
      this.saveCacheToStorage()
      
      console.log(`💾 [CACHE] Cached data: ${key} (${Math.round(dataSize / 1024)}KB)`)
      
    } catch (error) {
      this.logger.error('Cache set failed', 'set', { key, error })
    }
  }

  /**
   * Check if key exists in any cache tier
   */
  async exists(key: string): Promise<boolean> {
    if (this.hotCache.has(key)) return true
    if (this.warmCacheStorage.has(key)) return true
    return this.coldCacheStorage.has(key)
  }

  /**
   * Remove data from all cache tiers
   */
  async remove(key: string): Promise<void> {
    try {
      // Remove from hot cache
      this.hotCache.delete(key)

      // Remove from warm cache
      this.warmCacheStorage.delete(key)

      // Remove from cold cache
      this.coldCacheStorage.delete(key)

      // Persist changes to localStorage
      this.saveCacheToStorage()

      console.log(`🗑️ [CACHE] Removed from cache: ${key}`)

    } catch (error) {
      this.logger.error('Cache remove failed', 'remove', { key, error })
    }
  }

  /**
   * Get cache statistics
   */
  async getStats(): Promise<CacheStats & { hitRate: number }> {
    const hitRate = this.stats.totalRequests > 0
      ? (this.stats.hits / this.stats.totalRequests) * 100
      : 0

    const warmCacheSize = await this.calculateDirectorySize(this.warmCachePath)
    const coldCacheSize = await this.calculateDirectorySize(this.coldCachePath)

    return {
      hotCacheSize: this.getHotCacheSize(),
      warmCacheSize: warmCacheSize,
      coldCacheSize: coldCacheSize,
      totalEntries: this.hotCache.size,
      hitRate: hitRate,
      evictions: this.stats.evictions
    }
  }

  /**
   * Clear all caches
   */
  async clear(): Promise<void> {
    try {
      // Clear hot cache
      this.hotCache.clear()

      // Clear warm cache directory
      await this.clearDirectory(this.warmCachePath)

      // Clear cold cache directory
      await this.clearDirectory(this.coldCachePath)

      console.log(`🧹 [CACHE] All caches cleared`)

    } catch (error) {
      this.logger.error('Cache clear failed', 'clear', error)
    }
  }

  // Private helper methods

  private async setHotCache<T>(key: string, data: T): Promise<void> {
    const size = this.estimateSize(data)
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      accessCount: 1,
      lastAccessed: Date.now(),
      size
    }
    
    this.hotCache.set(key, entry)
    
    // Check if we need to evict
    if (this.getHotCacheSize() > this.config.hotCacheMaxSize) {
      await this.evictFromHotCache()
    }
  }



  private getHotCacheSize(): number {
    let totalSize = 0
    for (const entry of this.hotCache.values()) {
      totalSize += entry.size
    }
    return totalSize
  }

  private estimateSize(data: any): number {
    try {
      return JSON.stringify(data).length * 2 // Rough estimate
    } catch {
      return 1024 // Default 1KB
    }
  }

  private async evictFromHotCache(): Promise<void> {
    // LRU eviction: remove least recently used entries
    const entries = Array.from(this.hotCache.entries())
    entries.sort((a, b) => a[1].lastAccessed - b[1].lastAccessed)
    
    // Remove oldest 25% of entries
    const toRemove = Math.ceil(entries.length * 0.25)
    for (let i = 0; i < toRemove; i++) {
      this.hotCache.delete(entries[i][0])
      this.stats.evictions++
    }
    
    console.log(`🧹 [CACHE] Evicted ${toRemove} entries from hot cache`)
  }

  private async performMaintenance(): Promise<void> {
    try {
      // Clean up expired entries
      const now = Date.now()
      const expired: string[] = []

      for (const [key, entry] of this.hotCache.entries()) {
        if (now - entry.timestamp > this.config.maxAge) {
          expired.push(key)
        }
      }

      for (const key of expired) {
        this.hotCache.delete(key)
      }

      if (expired.length > 0) {
        console.log(`🧹 [CACHE] Cleaned up ${expired.length} expired entries`)
      }

      // Check cache sizes and perform cleanup if needed
      this.performCacheSizeCleanup()

      // Persist cache to localStorage
      this.saveCacheToStorage()

    } catch (error) {
      this.logger.error('Cache maintenance failed', 'performMaintenance', error)
    }
  }

  private async calculateDirectorySize(dirPath: string): Promise<number> {
    try {
      if (!await fsExists(dirPath)) return 0

      const files = await fsReaddir(dirPath)
      let totalSize = 0

      for (const file of files) {
        const filePath = path.join(dirPath, file)
        const stats = await fsStat(filePath)
        totalSize += stats.size
      }

      return totalSize
    } catch (error) {
      return 0
    }
  }

  private async clearDirectory(dirPath: string): Promise<void> {
    try {
      if (!await fsExists(dirPath)) return

      const files = await fsReaddir(dirPath)
      for (const file of files) {
        const filePath = path.join(dirPath, file)
        await fsUnlink(filePath)
      }
    } catch (error) {
      this.logger.error('Failed to clear directory', 'clearDirectory', { dirPath, error })
    }
  }

  private async performCacheSizeCleanup(): Promise<void> {
    try {
      const warmSize = await this.calculateDirectorySize(this.warmCachePath)
      const coldSize = await this.calculateDirectorySize(this.coldCachePath)

      // Clean up warm cache if it exceeds limit
      if (warmSize > this.config.warmCacheMaxSize) {
        await this.cleanupOldestFiles(this.warmCachePath, warmSize - this.config.warmCacheMaxSize)
      }

      // Clean up cold cache if it exceeds limit
      if (coldSize > this.config.coldCacheMaxSize) {
        await this.cleanupOldestFiles(this.coldCachePath, coldSize - this.config.coldCacheMaxSize)
      }
    } catch (error) {
      this.logger.error('Cache size cleanup failed', 'performCacheSizeCleanup', error)
    }
  }

  private async cleanupOldestFiles(dirPath: string, bytesToFree: number): Promise<void> {
    try {
      if (!await fsExists(dirPath)) return

      const files = await fsReaddir(dirPath)
      const fileStats: Array<{ name: string, path: string, mtime: Date, size: number }> = []

      for (const file of files) {
        const filePath = path.join(dirPath, file)
        const stats = await fsStat(filePath)
        fileStats.push({
          name: file,
          path: filePath,
          mtime: stats.mtime,
          size: stats.size
        })
      }

      // Sort by modification time (oldest first)
      fileStats.sort((a, b) => a.mtime.getTime() - b.mtime.getTime())

      let freedBytes = 0
      let deletedFiles = 0

      for (const file of fileStats) {
        if (freedBytes >= bytesToFree) break

        await fsUnlink(file.path)
        freedBytes += file.size
        deletedFiles++
      }

      if (deletedFiles > 0) {
        console.log(`🧹 [CACHE] Cleaned up ${deletedFiles} old files, freed ${Math.round(freedBytes / 1024 / 1024)}MB`)
      }
    } catch (error) {
      this.logger.error('Failed to cleanup old files', 'cleanupOldestFiles', { dirPath, error })
    }
  }
}

export const cacheManager = new CacheManager()
