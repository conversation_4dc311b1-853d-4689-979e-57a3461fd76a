Search files...

Ran tool

Search files...

Okay — you can get tight, few‑word ideas by controlling three layers: model parameters, prompt hardening, and post‑processing enforcement. Here’s exactly where and what to change.

### 1) Add model controls and pipe them to the local providers
- Add these to `FileIntelligenceConfig`: `temperature`, `top_p`, `top_k`, `max_tokens`, `force_json` (boolean), `stop?: string[]`.
- Default recommendations:
  - temperature: 0.1
  - top_p: 0.9
  - top_k: 40
  - max_tokens: 512
  - force_json: true
  - stop: ["```"]
- Pass them through when calling local models:
  - In `localModelService`:
    - Ollama: include `options` and `format: "json"` in the chat body; also pass `stop`, `num_predict` mapped from `max_tokens`.
    - LM Studio: include `temperature`, `top_p`, `max_tokens`, `stop`, and if supported, `response_format: { type: "json_object" }`.

Where to look:
- Local provider call sites:
```startLine:245:endLine:266:src/services/localModelService.ts
private async sendOllamaMessage(
  model: string,
  messages: Array<{ role: string; content: string }>,
  onChunk?: (chunk: string) => void
) { ... body: JSON.stringify({ model, messages, stream: !!onChunk }) }
```

### 2) Harden the prompt to force short keywords
- Update `buildKeyIdeaExtractionPrompt(...)` to include stricter rules:
  - “Return ONLY JSON (no prose, no markdown), exactly N items.”
  - “Each key_ideas[i].text must be 1–4 words, no punctuation, no verbs; noun or noun phrase.”
  - “Names/emails/companies → set intent_types: [\"connection\"]”
  - “Do not include explanations or extra fields.”
Where to change:
```startLine:218:endLine:247:src/services/fileIntelligenceService.ts
private buildKeyIdeaExtractionPrompt(content: string, minIdeas: number): string { ... }
```

### 3) Add a system message and JSON mode
- In `extractKeyIdeasWithLocalModel(...)`, send messages with a system role first:
  - system: “You output strict JSON only. Generate minimal keywords as specified.”
- If `force_json` is true, set provider JSON mode (Ollama `format: "json"`, LM Studio `response_format` when available).

Where to adjust message composition:
```startLine:171:endLine:200:src/services/fileIntelligenceService.ts
const response = await localModelService.sendMessage(
  config.local_model_preferred,
  messages
)
```

### 4) Enforce constraints in post‑processing
- In `parseKeyIdeasFromResponse(...)`:
  - Keep only items with 1–4 words; strip punctuation; Title‑Case or lower‑case consistently.
  - De‑duplicate by normalized text.
  - If any item violates rules or count < min, backfill using `extractKeyIdeasKeywordBased(...)`.
- This function already filters long/descriptive text; extend it to hard‑clamp word count and normalize.

Where to add enforcement:
```startLine:253:endLine:339:src/services/fileIntelligenceService.ts
private parseKeyIdeasFromResponse(...) { ... }
```

### 5) Use entities to guarantee human connections show up
- After generating ideas, inject top people/emails/companies (from `extractWeightedEntities` and `extractHumanConnections`) as ideas with `intent_types: ["connection"]` and high relevance if none were returned by the model.

Where to combine:
- Right after ideas are parsed and before storage (in `analyzeFile`/`analyzeDocument`), check for at least N “connection” ideas; if missing, synthesize from extracted entities.

### 6) Model choice tips
- Prefer instruction‑following local instruct models. Start with your current Gemma‑instruct; if ideas are still verbose, try a smaller, stricter instruct variant. Keep temperature low (0.1–0.2).

### 7) Quick test settings (no UI changes required)
- In your call site (e.g., `FilePageOverlay.tsx`), pass the extra config:
  - temperature: 0.1, top_p: 0.9, top_k: 40, max_tokens: 512, force_json: true.
- Run Organize on one `.md` and one `.pdf`, then inspect `.context/files/*.json` to confirm:
  - `key_ideas[*].text` are 1–4 words,
  - `intent_types` include `["connection"]` for names/emails,
  - no prose or punctuation in idea text.

- Control levers: add generation params to `FileIntelligenceConfig`, pipe to `localModelService` (Ollama/LM Studio), and set JSON mode.
- Enforcement: harden prompt + system message, and clamp outputs in `parseKeyIdeasFromResponse`; fallback fills gaps.
- Outcome: consistently short, noun‑phrase ideas; reliable author/contact inclusion via entity injection.