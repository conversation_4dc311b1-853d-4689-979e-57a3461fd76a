/**
 * Batch File Processing Service
 * Handles batch processing of files in context vaults for organization
 */

import { BaseService, ServiceError, ServiceErrorCode } from './base'
import { fileAnalysisService } from './fileAnalysisService'
import { vaultUIManager } from './vaultUIManager'
import { fileProcessingQueue } from './fileProcessingQueue'
import {
  BatchProcessingStatus,
  ProcessingResult,
  FileIntelligenceConfig,
  DEFAULT_FILE_INTELLIGENCE_CONFIG
} from '../types/fileIntelligenceTypes'

export interface VaultProcessingOptions {
  vaultPath: string
  vaultName: string
  fileExtensions?: string[] // Filter by file types
  maxFiles?: number // Limit number of files to process
  config?: Partial<FileIntelligenceConfig>
}

class BatchFileProcessingService extends BaseService {
  private currentBatch: BatchProcessingStatus | null = null
  private processingAborted = false

  constructor() {
    super({
      name: 'BatchFileProcessingService',
      autoInitialize: true
    })
  }

  protected async doInitialize(): Promise<void> {
    this.logger.info('Batch File Processing Service initialized', 'doInitialize')
  }

  /**
   * Process all files in a context vault
   */
  async processVault(options: VaultProcessingOptions): Promise<BatchProcessingStatus> {
    return await this.executeOperationOrThrow(
      'processVault',
      async () => {
        this.logger.info('Starting vault processing', 'processVault', options)

        // Reset processing state
        this.processingAborted = false
        
        // Get all files in the vault
        const files = await this.getVaultFiles(options.vaultPath, options.fileExtensions)
        
        if (files.length === 0) {
          throw new ServiceError(
            ServiceErrorCode.VALIDATION_ERROR,
            'No processable files found in vault',
            { serviceName: this.serviceName, operation: 'processVault', vaultPath: options.vaultPath }
          )
        }

        // Limit files if specified
        const filesToProcess = options.maxFiles ? files.slice(0, options.maxFiles) : files
        
        // Initialize batch status
        this.currentBatch = {
          total_files: filesToProcess.length,
          processed_files: 0,
          failed_files: 0,
          current_file: undefined,
          overall_progress: 0,
          start_time: new Date().toISOString(),
          results: []
        }

        // Emit initial progress
        this.emitProgress()

        // Process files sequentially to avoid overwhelming the system
        for (let i = 0; i < filesToProcess.length; i++) {
          if (this.processingAborted) {
            this.logger.info('Processing aborted by user', 'processVault')
            break
          }

          const file = filesToProcess[i]
          this.currentBatch.current_file = file.name

          try {
            // Read file content
            const content = await this.readFileContent(file.path)
            
            // Process with AI analysis and store results
            const result = await fileAnalysisService.analyzeAndStoreDocument(
              content,
              file.path,
              options.vaultPath,
              {
                ...DEFAULT_FILE_INTELLIGENCE_CONFIG,
                ...options.config
              }
            )

            // Create processing result
            const processingResult: ProcessingResult = {
              success: true,
              file_path: file.path,
              processing_time_ms: result.analysis_metadata.processing_time_ms,
              ideas_extracted: result.key_ideas.length,
              entities_found: result.weighted_entities.length,
              human_connections: result.human_connections.length,
              confidence_score: result.processing_confidence,
              local_model_used: result.analysis_metadata.model_used
            }

            this.currentBatch.results.push(processingResult)
            this.currentBatch.processed_files++

            this.logger.info('File processed successfully', 'processVault', {
              file: file.name,
              ideasExtracted: result.key_ideas.length,
              entitiesFound: result.weighted_entities.length
            })

          } catch (error) {
            this.logger.error('Failed to process file', 'processVault', { file: file.name, error })
            
            const failedResult: ProcessingResult = {
              success: false,
              file_path: file.path,
              processing_time_ms: 0,
              ideas_extracted: 0,
              entities_found: 0,
              human_connections: 0,
              confidence_score: 0,
              error_message: error instanceof Error ? error.message : 'Unknown error'
            }

            this.currentBatch.results.push(failedResult)
            this.currentBatch.failed_files++
          }

          // Update progress
          this.currentBatch.overall_progress = Math.round((i + 1) / filesToProcess.length * 100)
          this.emitProgress()

          // Small delay to prevent overwhelming the system
          await new Promise(resolve => setTimeout(resolve, 100))
        }

        // Calculate estimated completion time
        const endTime = new Date()
        this.currentBatch.estimated_completion = endTime.toISOString()
        this.currentBatch.current_file = undefined

        this.logger.info('Vault processing completed', 'processVault', {
          totalFiles: this.currentBatch.total_files,
          processedFiles: this.currentBatch.processed_files,
          failedFiles: this.currentBatch.failed_files,
          successRate: Math.round((this.currentBatch.processed_files / this.currentBatch.total_files) * 100)
        })

        // Emit final progress
        this.emitProgress()

        return { ...this.currentBatch }
      },
      options
    )
  }

  /**
   * Get all processable files in a vault (recursive, with exclusions)
   */
  private async getVaultFiles(vaultPath: string, extensions?: string[]): Promise<Array<{ name: string; path: string; size: number }>> {
    const defaultExtensions = [
      // Documents
      'pdf', 'md', 'txt', 'doc', 'docx', 'rtf',
      // Data files
      'json', 'csv', 'xml', 'yaml', 'yml', 'log',
      // Code files
      'js', 'ts', 'tsx', 'jsx', 'html', 'css', 'scss', 'less',
      'py', 'java', 'cpp', 'c', 'h', 'cs', 'php', 'rb', 'go', 'rs',
      // Config files
      'ini', 'conf', 'config', 'env', 'properties',
      // Images (for OCR processing)
      'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp', 'svg', 'tiff',
      // Spreadsheets
      'xlsx', 'xls', 'ods',
      // Presentations
      'pptx', 'ppt', 'odp',
      // Archives (for content extraction)
      'zip', 'rar', '7z', 'tar', 'gz'
    ]
    const allowedExtensions = extensions || defaultExtensions

    // Directories and files to exclude
    const excludedDirs = ['.vault', '.context', 'node_modules', '.git']
    const excludedFiles = ['master.md', '.gitignore', '.DS_Store']

    try {
      console.log(`🚀 [VAULT-SCAN] Starting recursive vault scan for: ${vaultPath}`)
      console.log(`🚀 [VAULT-SCAN] Allowed extensions:`, allowedExtensions)
      console.log(`🚀 [VAULT-SCAN] Excluded directories:`, excludedDirs)
      console.log(`🚀 [VAULT-SCAN] Excluded files:`, excludedFiles)

      this.logger.info('Starting recursive vault scan', 'getVaultFiles', {
        vaultPath,
        allowedExtensions,
        excludedDirs,
        excludedFiles
      })

      const allFiles = await this.scanDirectoryRecursive(vaultPath, allowedExtensions, excludedDirs, excludedFiles)

      console.log(`🎯 [VAULT-SCAN] Recursive scan complete for ${vaultPath}`)
      console.log(`🎯 [VAULT-SCAN] Total files found: ${allFiles.length}`)
      console.log(`🎯 [VAULT-SCAN] Files:`, allFiles.map(f => ({ name: f.name, path: f.path, size: f.size })))

      this.logger.info('Recursive scan complete', 'getVaultFiles', {
        totalFiles: allFiles.length,
        fileNames: allFiles.map(f => f.name)
      })

      return allFiles
    } catch (error) {
      console.error(`💥 [VAULT-SCAN] Failed to get vault files for ${vaultPath}:`, error)
      this.logger.error('Failed to get vault files', 'getVaultFiles', error)
      throw error
    }
  }

  /**
   * Recursively scan directory for processable files
   */
  private async scanDirectoryRecursive(
    dirPath: string,
    allowedExtensions: string[],
    excludedDirs: string[],
    excludedFiles: string[]
  ): Promise<Array<{ name: string; path: string; size: number }>> {
    const results: Array<{ name: string; path: string; size: number }> = []

    try {
      console.log(`🔍 [SCAN] Starting scan of directory: ${dirPath}`)

      if (!window.electronAPI?.vault?.readDirectory) {
        console.error('❌ [SCAN] Electron API not available for directory scanning')
        this.logger.warn('Electron API not available for directory scanning', 'scanDirectoryRecursive')
        return results
      }

      const result = await window.electronAPI.vault.readDirectory(dirPath)

      if (!result.success) {
        console.error(`❌ [SCAN] Failed to read directory: ${dirPath}`, result.error)
        this.logger.warn('Failed to read directory', 'scanDirectoryRecursive', {
          dirPath,
          error: result.error
        })
        return results
      }

      const items = result.items || []
      console.log(`📁 [SCAN] Found ${items.length} items in ${dirPath}:`, items.map(i => `${i.name} (${i.type})`))

      for (const item of items) {
        console.log(`🔎 [SCAN] Processing item: ${item.name} (${item.type})`)

        // Skip excluded directories and files
        if (excludedDirs.includes(item.name) || excludedFiles.includes(item.name)) {
          console.log(`⏭️ [SCAN] Skipping excluded item: ${item.name}`)
          this.logger.debug('Skipping excluded item', 'scanDirectoryRecursive', {
            itemName: item.name,
            type: item.type
          })
          continue
        }

        // Handle both explicit type and fallback logic for undefined types
        const isDirectory = item.type === 'directory' || (item.type === undefined && !item.name.includes('.'))
        const isFile = item.type === 'file' || (item.type === undefined && item.name.includes('.'))

        console.log(`🔍 [SCAN] Item analysis: ${item.name}, type: ${item.type}, isDirectory: ${isDirectory}, isFile: ${isFile}`)

        if (isDirectory) {
          console.log(`📂 [SCAN] Entering subdirectory: ${item.name}`)
          // Recursively scan subdirectory
          const subDirPath = `${dirPath}/${item.name}`
          const subFiles = await this.scanDirectoryRecursive(subDirPath, allowedExtensions, excludedDirs, excludedFiles)
          console.log(`📂 [SCAN] Found ${subFiles.length} files in subdirectory: ${item.name}`)
          results.push(...subFiles)
        } else if (isFile) {
          // Check if file has allowed extension
          const extension = item.name.split('.').pop()?.toLowerCase()
          console.log(`📄 [SCAN] File: ${item.name}, extension: ${extension}, allowed: ${allowedExtensions.includes(extension || '')}`)

          if (extension && allowedExtensions.includes(extension)) {
            const fileInfo = {
              name: item.name,
              path: `${dirPath}/${item.name}`,
              size: item.size || 0
            }
            console.log(`✅ [SCAN] Added processable file:`, fileInfo)
            results.push(fileInfo)
          } else {
            console.log(`❌ [SCAN] Skipping file with unsupported extension: ${item.name}`)
          }
        } else {
          console.log(`❓ [SCAN] Unknown item type, skipping: ${item.name}`)
        }
      }

      console.log(`📊 [SCAN] Directory scan complete for ${dirPath}: ${results.length} processable files found`)

    } catch (error) {
      console.error(`💥 [SCAN] Error during recursive directory scan of ${dirPath}:`, error)
      this.logger.error('Error during recursive directory scan', 'scanDirectoryRecursive', {
        dirPath,
        error
      })
    }

    return results
  }

  /**
   * Read file content using Electron API
   */
  private async readFileContent(filePath: string): Promise<string> {
    try {
      if (window.electronAPI?.vault?.readFile) {
        const result = await window.electronAPI.vault.readFile(filePath)
        
        if (!result.success) {
          throw new Error(`Failed to read file: ${result.error}`)
        }

        return result.content || ''
      }

      throw new Error('Electron API not available')
    } catch (error) {
      this.logger.error('Failed to read file content', 'readFileContent', { filePath, error })
      throw error
    }
  }

  /**
   * Emit progress event for UI updates
   */
  private emitProgress(): void {
    if (this.currentBatch && typeof window !== 'undefined' && window.electronAPI?.events?.emit) {
      window.electronAPI.events.emit('batch-processing-progress', this.currentBatch)
    }
  }

  /**
   * Get current batch processing status
   */
  getCurrentBatchStatus(): BatchProcessingStatus | null {
    return this.currentBatch ? { ...this.currentBatch } : null
  }

  /**
   * Abort current processing
   */
  abortProcessing(): void {
    this.processingAborted = true
    this.logger.info('Processing abort requested', 'abortProcessing')
  }

  /**
   * Process all vaults using the queue system (recommended)
   */
  async processAllVaultsWithQueue(config?: Partial<FileIntelligenceConfig>): Promise<string[]> {
    return await this.executeOperationOrThrow(
      'processAllVaultsWithQueue',
      async () => {
        this.logger.info('Starting queued vault processing', 'processAllVaultsWithQueue')

        // Get vault registry (same logic as processAllVaults)
        const registry = await vaultUIManager.getVaultRegistry()

        this.logger.info('Vault registry retrieved for queue processing', 'processAllVaultsWithQueue', {
          hasRegistry: !!registry,
          vaultCount: registry?.vaults?.length || 0,
          vaults: registry?.vaults?.map(v => ({ name: v.name, path: v.path })) || []
        })

        let vaults: Array<{ name: string; path: string }> = []

        if (!registry || !registry.vaults || registry.vaults.length === 0) {
          // Fallback to mock vaults
          console.log(`⚠️ [VAULT-DISCOVERY] No vault registry found, using mock vaults for queue processing`)
          this.logger.warn('No vault registry found, using mock vaults for queue processing', 'processAllVaultsWithQueue')
          vaults = [
            {
              name: 'Work Vault',
              path: 'C:\\Users\\<USER>\\Documents\\Test18\\work-vault'
            },
            {
              name: 'Personal Vault',
              path: 'C:\\Users\\<USER>\\Documents\\Test18\\personal-vault'
            }
          ]
          console.log(`🔧 [VAULT-DISCOVERY] Using mock vaults:`, vaults)
        } else {
          vaults = registry.vaults.map(v => ({ name: v.name, path: v.path }))
          console.log(`✅ [VAULT-DISCOVERY] Found ${vaults.length} registered vaults:`, vaults)
        }

        const taskIds: string[] = []

        for (const vault of vaults) {
          try {
            console.log(`🏗️ [QUEUE] Processing vault: ${vault.name} at ${vault.path}`)
            const files = await this.getVaultFiles(vault.path)

            console.log(`🏗️ [QUEUE] Vault ${vault.name} scan result: ${files.length} files found`)

            this.logger.info('Queueing vault files', 'processAllVaultsWithQueue', {
              vaultName: vault.name,
              totalFiles: files.length
            })

            if (files.length === 0) {
              console.log(`⚠️ [QUEUE] No files found in vault: ${vault.name}`)
            }

            // Add files to queue with priority based on file size (smaller files first)
            for (const file of files) {
              const priority = file.size < 1024 * 1024 ? 'high' : 'normal' // Files < 1MB get high priority
              console.log(`➕ [QUEUE] Adding file to queue: ${file.name} (${file.size} bytes, priority: ${priority})`)

              const taskId = await fileProcessingQueue.addTask(
                file.path,
                file.name,
                file.size,
                vault.path,
                priority
              )
              taskIds.push(taskId)
              console.log(`✅ [QUEUE] File queued with task ID: ${taskId}`)
            }

          } catch (error) {
            console.error(`💥 [QUEUE] Failed to queue vault files for ${vault.name}:`, error)
            this.logger.error('Failed to queue vault files', 'processAllVaultsWithQueue', {
              vaultName: vault.name,
              error
            })
          }
        }

        this.logger.info('All files queued for processing', 'processAllVaultsWithQueue', {
          totalTasks: taskIds.length,
          totalVaults: vaults.length
        })

        return taskIds
      },
      { totalVaults: 'unknown' }
    )
  }

  /**
   * Process all vaults in the registry (legacy method)
   */
  async processAllVaults(config?: Partial<FileIntelligenceConfig>): Promise<BatchProcessingStatus[]> {
    return await this.executeOperationOrThrow(
      'processAllVaults',
      async () => {
        this.logger.info('Starting processing of all vaults', 'processAllVaults')

        // Get vault registry
        const registry = await vaultUIManager.getVaultRegistry()

        this.logger.info('Vault registry retrieved', 'processAllVaults', {
          hasRegistry: !!registry,
          vaultCount: registry?.vaults?.length || 0,
          vaults: registry?.vaults?.map(v => ({ name: v.name, path: v.path })) || []
        })

        if (!registry || !registry.vaults || registry.vaults.length === 0) {
          // Fallback for development/testing - try to process mock vaults
          this.logger.warn('No vault registry found, attempting fallback to mock vaults', 'processAllVaults')
          return await this.processMockVaults()
        }

        const results: BatchProcessingStatus[] = []

        // Process each vault
        for (const vault of registry.vaults) {
          try {
            this.logger.info('Processing vault', 'processAllVaults', { vaultName: vault.name })
            
            const vaultResult = await this.processVault({
              vaultPath: vault.path,
              vaultName: vault.name,
              config
            })

            results.push(vaultResult)

            // Small delay between vaults
            await new Promise(resolve => setTimeout(resolve, 500))

          } catch (error) {
            this.logger.error('Failed to process vault', 'processAllVaults', { 
              vaultName: vault.name, 
              error 
            })
            
            // Create failed result for this vault
            const failedResult: BatchProcessingStatus = {
              total_files: 0,
              processed_files: 0,
              failed_files: 1,
              overall_progress: 0,
              start_time: new Date().toISOString(),
              results: [{
                success: false,
                file_path: vault.path,
                processing_time_ms: 0,
                ideas_extracted: 0,
                entities_found: 0,
                human_connections: 0,
                confidence_score: 0,
                error_message: error instanceof Error ? error.message : 'Unknown error'
              }]
            }
            
            results.push(failedResult)
          }
        }

        this.logger.info('Completed processing all vaults', 'processAllVaults', {
          totalVaults: registry?.vaults?.length || 0,
          successfulVaults: results.filter(r => r.failed_files === 0).length
        })

        return results
      },
      { totalVaults: 0 }
    )
  }

  /**
   * Fallback method to process mock vaults for development/testing
   */
  private async processMockVaults(): Promise<BatchProcessingStatus[]> {
    this.logger.info('Processing mock vaults as fallback', 'processMockVaults')

    // Use the vault root that the system is actually using
    const mockVaults = [
      {
        name: 'Work Vault',
        path: 'C:\\Users\\<USER>\\Documents\\Test18\\work-vault'
      },
      {
        name: 'Personal Vault',
        path: 'C:\\Users\\<USER>\\Documents\\Test18\\personal-vault'
      }
    ]

    const results: BatchProcessingStatus[] = []

    for (const vault of mockVaults) {
      try {
        this.logger.info('Processing mock vault', 'processMockVaults', { vaultName: vault.name })

        const vaultResult = await this.processVault({
          vaultPath: vault.path,
          vaultName: vault.name
        })

        results.push(vaultResult)

        // Small delay between vaults
        await new Promise(resolve => setTimeout(resolve, 500))

      } catch (error) {
        this.logger.error('Failed to process mock vault', 'processMockVaults', {
          vaultName: vault.name,
          error
        })

        // Auto-cleanup corrupted intelligence state for this vault
        try {
          const { intelligenceStorageService } = await import('./intelligenceStorageService')
          await intelligenceStorageService.clearVaultIntelligenceState(vault.path)
          this.logger.info('Cleared corrupted vault state', 'processMockVaults', { vaultName: vault.name })
        } catch (cleanupError) {
          this.logger.warn('Failed to cleanup vault state', 'processMockVaults', { vaultName: vault.name, cleanupError })
        }

        // Create failed result for this vault
        const failedResult: BatchProcessingStatus = {
          vault_name: vault.name,
          vault_path: vault.path,
          total_files: 0,
          processed_files: 0,
          failed_files: 1,
          current_file: undefined,
          start_time: new Date().toISOString(),
          end_time: new Date().toISOString(),
          processing_errors: [error instanceof Error ? error.message : String(error)],
          performance_metrics: {
            total_processing_time: 0,
            average_file_time: 0,
            files_per_second: 0
          }
        }

        results.push(failedResult)
      }
    }

    this.logger.info('Completed processing mock vaults', 'processMockVaults', {
      totalVaults: mockVaults.length,
      successfulVaults: results.filter(r => r.failed_files === 0).length
    })

    return results
  }
}

export const batchFileProcessingService = new BatchFileProcessingService()
