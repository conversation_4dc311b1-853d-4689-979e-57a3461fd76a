# UI Design Preferences
- User prefers dark theme (neutral-950 bg), Inter font, Tailwind CSS with modern design patterns including backdrop blur, rounded corners, and indigo accent colors. ChatLo design system uses color palette: primary #8AB0BB (teal), secondary #FF8383 (coral), tertiary #1B3E68 (navy), with supplement colors.
- User wants improved model selection UI with filtering by type, collapsible reasoning output, system prompt support, higher max tokens, and comprehensive LLM configuration controls including temperature, top-P, and top-K settings.
- User prefers chat settings in an overlayed lightbox pattern utilizing 80% of screen space, with model supplier and context window on one line, Ship icon for Flagship models, and star-based favorite model bookmarking.
- User wants optimized file attachment experience with lazy loading, proper document handling, and vectorization workflow with user confirmation. File picker UI should have 'Browse' and 'Confirm' buttons placed next to each other.
- User wants artifacts area to slide-in when chat contains documents, with inline buttons for images/code/markdown, expandable to fullscreen, with copy/run/edit capabilities. Artifacts should maintain left document list with proper FontAwesome icons.
- User wants ChatLo to compete with VSCode/system apps with: exciting homepage top boxes, robust search like macOS Spotlight/VSCode commands, shadcn/ui quality cards, unified VSCode-style footer across all pages, fixed vertical bar navigation, professional chat spacing with better context menus, comprehensive file handling with in-app viewers for PDF/markdown/code/images and system defaults for office files, plus flexible right-click context menus for future AI features.
- User wants Files Page to maintain Explorer mode alongside new hub, with hub opening only for supported file types and close button returning to Explorer mode at the same location.
- User wants Files Page right panel to be collapsible and resizable with minimum width, file viewer to support PDF/image viewing with edit capabilities for text/markdown files, context menus with smart deep linking (e.g., chat with document should create new chat with attached parsed document), and expects Windows OS-level competition quality.
- User prefers document viewer to start with PDF and Markdown support first, with intelligent file type detection logic to determine how to parse and pass different file types to AI for processing.
- User wants comprehensive right-click context menu for files with Preview/Edit in FilePageOverlay.tsx, standard IPC calls for Copy Path/Show in Explorer, AI Actions submenu (Summarize, Extract Key Points, Create Chat Context, Generate Questions), and Vault Actions submenu (Move/Copy to Vault, Add to Master.md).
- User wants edit mode in FilePageOverlay with edit icon after download button, context menu should include Rename option with id-badge icon, and file editing should use same mechanism as New File but with filename popup.

# File System & Context Vaults
- User prefers file-based context vault over database storage, with auto-created subfolders and master.md for each context, optimized for Gemma3n (32k) and Gemma3 (128k) models.
- ChatLo vault files should follow path pattern: <prefix> + <folder in settings> + <vault name> + <folder template>, not treated as temporary files.
- ChatLo has multiple inconsistent file handling entry points that need unified architecture for vault-oriented file processing due to complexity with large files, multiple touchpoints, and messy API calls.
- ChatLo Step 3 completed with critical file corruption issues: aggressive filename sanitization destroying original names, binary file integrity problems in base64 encoding, and no file integrity verification.
- User prefers vault-centric operations where anything in context vaults opens to exact location with vault focused, and wants vault context overview modal with 3-column layout for recent chats, files, and master.md view.
- User wants unified context vault management with reusable dropdown components, toast notifications, state persistence for vault selection, and focused file tree display showing only selected vault contents.
- For vault organization, scan all folders recursively but exclude ./vault, ./context directories and master.md file since these are for results/metadata, not source data.

# Functionality Requirements
- User wants to add database functionality for chat history and favorite model bookmarking, with OTA update capability and dynamic model updates using JSON files from OpenRouter.
- Private mode logic: when off, all models (including external) are accessible with WiFi on; when on, prompt user to install local model if unavailable. Private Mode OFF should automatically go Online with appropriate WiFi icon states.
- User prefers that LM Studio and Ollama connection failures should not block the app from running properly and wants graceful fallback when local model services are unavailable.
- User wants chat naming automation using first question as chat name instead of 'New Conversation', with expanded search across all content including generated titles.
- User wants to implement a structured dialogue recording framework with 8 specific personas for app development meetings, including turn limits and cooldown periods.
- User wants file intelligence to extract 10+ key ideas with relevance % scores, prioritize human connections (names, emails, companies, titles), use local models for analysis, and support co-existing intent types (topic, category, connections) with weighted data priorities.

# Development Approach
- User's ChatLocal app is Electron-first with no database design for web end and limited route navigation capabilities.
- User prefers UI-first development workflow: implement UI additions first for interim review, then add functionality code only after UI approval.
- User prefers structured development with detailed to-do lists for approval before implementing changes, and wants to optimize app size below 100MB for downloadable distribution.
- User prefers console debug messages over browser testing for verification, and wants to see actual functionality working before claiming tasks are complete.
- User prefers FontAwesome module imports over CDN for better code organization, with proper class names, sizing, positioning, and styling from design specifications.
- User finds current in-app navigation insufficient compared to system explorer and wants comprehensive navigation analysis and improvement plan.
- Always lookup @c:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo/docs\API_REFERENCE.md before development. Minimize the empty calls when develops
- User prefers feature stories written in natural language to minimize misunderstandings during development. Create @c:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo/docs\feature-story\ FOLDER and always refer to README.md before development.

# Settings & Configuration
- Settings page should have User Profile/Billing/API Setup/Data Management/Logout sections, with User Preference section including separate default model menus for normal and private modes.
- User prefers API keys and ports configured in Settings page rather than .env files for BYOK hybrid release model, handling ChatLo key, OpenRouter API key, Ollama, and LM Studio configurations.
- User wants comprehensive technical documentation including IPC, state management, and database management, plus documentation of Electron app challenges with focus on data safeguarding and stability.